<?php
/**
 * Database Connection Test for dribs.xyz
 */

echo "<h1>Database Connection Test</h1>";

// Database credentials
$db_name = 'jobzcsdn_login';
$db_user = 'jobzcsdn_login';
$db_pass = '47G28{lIlsf-';
$db_host = 'localhost';

echo "<h2>Database Credentials:</h2>";
echo "<p><strong>Database:</strong> $db_name</p>";
echo "<p><strong>Username:</strong> $db_user</p>";
echo "<p><strong>Password:</strong> " . str_repeat('*', strlen($db_pass)) . "</p>";
echo "<p><strong>Host:</strong> $db_host</p>";

echo "<h2>Connection Test:</h2>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Check if WordPress tables exist
    echo "<h2>WordPress Tables Check:</h2>";
    
    $tables_to_check = ['wp_posts', 'wp_options', 'wp_users'];
    
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch();
        
        if ($exists) {
            echo "<p style='color: green;'>✅ Table '$table' exists</p>";
            
            if ($table === 'wp_posts') {
                // Count posts
                $count_stmt = $pdo->prepare("SELECT COUNT(*) FROM $table WHERE post_status = 'publish' AND post_type = 'post'");
                $count_stmt->execute();
                $post_count = $count_stmt->fetchColumn();
                echo "<p>📝 Published posts: $post_count</p>";
            }
            
            if ($table === 'wp_options') {
                // Check site URLs
                $url_stmt = $pdo->prepare("SELECT option_name, option_value FROM $table WHERE option_name IN ('siteurl', 'home')");
                $url_stmt->execute();
                $urls = $url_stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($urls as $url) {
                    echo "<p><strong>{$url['option_name']}:</strong> {$url['option_value']}</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>❌ Table '$table' not found</p>";
        }
    }
    
    echo "<h2>Next Steps:</h2>";
    echo "<ul>";
    echo "<li>If tables exist but URLs are wrong, run fix-wordpress-urls.php</li>";
    echo "<li>If tables don't exist, import your WordPress database</li>";
    echo "<li>If posts count is 0, create some posts in WordPress admin</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database connection failed!</p>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<h2>Common Solutions:</h2>";
    echo "<ul>";
    echo "<li>Check if database credentials are correct</li>";
    echo "<li>Verify database exists on hosting server</li>";
    echo "<li>Import your WordPress database if not done yet</li>";
    echo "<li>Contact hosting support if issue persists</li>";
    echo "</ul>";
}
?>
