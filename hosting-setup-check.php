<?php
/**
 * Hosting Setup Verification Script
 * Run this on hosting after uploading files and importing database
 */

echo "<h1>🚀 Hosting Setup Verification</h1>";

// Hosting Database credentials
$db_name = 'jobzcsdn_login';
$db_user = 'jobzcsdn_login';
$db_pass = '47G28{lIlsf-';
$db_host = 'localhost';

echo "<h2>📋 Environment Check:</h2>";
echo "<p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

echo "<h2>📁 File Structure Check:</h2>";
$required_files = [
    'wp/wp-config.php',
    'wp/wp-load.php', 
    'wp/index.php',
    'pages/blog.php',
    'config/config.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $file missing</p>";
    }
}

echo "<h2>🗄️ Database Connection Test:</h2>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Check WordPress tables
    echo "<h3>WordPress Tables:</h3>";
    $wp_tables = ['wp_posts', 'wp_options', 'wp_users', 'wp_postmeta', 'wp_terms', 'wp_term_taxonomy'];
    
    $tables_exist = 0;
    foreach ($wp_tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            echo "<p style='color: green;'>✅ $table exists</p>";
            $tables_exist++;
        } else {
            echo "<p style='color: red;'>❌ $table missing</p>";
        }
    }
    
    if ($tables_exist === 0) {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3 style='color: red;'>⚠️ No WordPress Tables Found!</h3>";
        echo "<p>Database is empty. You need to:</p>";
        echo "<ol>";
        echo "<li>Export database from XAMPP using phpMyAdmin</li>";
        echo "<li>Import the .sql file into hosting database</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        // Check posts
        echo "<h3>Content Check:</h3>";
        $post_stmt = $pdo->prepare("SELECT COUNT(*) FROM wp_posts WHERE post_status = 'publish' AND post_type = 'post'");
        $post_stmt->execute();
        $post_count = $post_stmt->fetchColumn();
        echo "<p>📝 Published posts: $post_count</p>";
        
        // Check URLs
        echo "<h3>URL Configuration:</h3>";
        $url_stmt = $pdo->prepare("SELECT option_name, option_value FROM wp_options WHERE option_name IN ('siteurl', 'home')");
        $url_stmt->execute();
        $urls = $url_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $urls_correct = true;
        foreach ($urls as $url) {
            echo "<p><strong>{$url['option_name']}:</strong> {$url['option_value']}</p>";
            if (strpos($url['option_value'], 'localhost') !== false) {
                $urls_correct = false;
            }
        }
        
        if (!$urls_correct) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3 style='color: orange;'>⚠️ URLs Need Update!</h3>";
            echo "<p>Run: <a href='fix-wordpress-urls.php'>fix-wordpress-urls.php</a></p>";
            echo "</div>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database connection failed!</p>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Possible Issues:</h3>";
    echo "<ul>";
    echo "<li>Database credentials incorrect</li>";
    echo "<li>Database not created on hosting</li>";
    echo "<li>Database not imported yet</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h2>🔧 WordPress API Test:</h2>";
$api_url = 'https://dribs.xyz/wp/wp-json/wp/v2/posts';
echo "<p>Testing: <a href='$api_url' target='_blank'>$api_url</a></p>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($api_url, false, $context);
$http_code = 200;

if (isset($http_response_header)) {
    preg_match('/HTTP\/\d\.\d\s+(\d+)/', $http_response_header[0], $matches);
    $http_code = isset($matches[1]) ? (int)$matches[1] : 500;
}

if ($http_code === 200) {
    echo "<p style='color: green;'>✅ WordPress API working!</p>";
    $posts = json_decode($response, true);
    if (is_array($posts)) {
        echo "<p>📝 API returned " . count($posts) . " posts</p>";
    }
} else {
    echo "<p style='color: red;'>❌ WordPress API failed (HTTP: $http_code)</p>";
}

echo "<h2>📋 Action Items:</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
echo "<h3>If Database is Empty:</h3>";
echo "<ol>";
echo "<li>Run <a href='export-database.php'>export-database.php</a> on XAMPP</li>";
echo "<li>Export database from XAMPP phpMyAdmin</li>";
echo "<li>Import .sql file to hosting phpMyAdmin</li>";
echo "<li>Re-run this script</li>";
echo "</ol>";

echo "<h3>If Database Exists but URLs Wrong:</h3>";
echo "<ol>";
echo "<li>Run <a href='fix-wordpress-urls.php'>fix-wordpress-urls.php</a></li>";
echo "<li>Login to WordPress admin</li>";
echo "<li>Update permalinks</li>";
echo "</ol>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
</style>
