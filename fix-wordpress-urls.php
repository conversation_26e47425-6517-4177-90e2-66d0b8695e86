<?php
/**
 * WordPress URL Fixer for Live Hosting
 * Run this script once after uploading to live server
 */

// Include WordPress config
require_once 'wp/wp-config.php';

echo "<h1>WordPress URL Fixer</h1>";
echo "<p>This script will update WordPress URLs from localhost to live domain.</p>";

// Get current environment
$http_host = $_SERVER['HTTP_HOST'];
$is_local = (strpos($http_host, 'localhost') !== false || strpos($http_host, '127.0.0.1') !== false);
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';

echo "<h2>Environment Detection:</h2>";
echo "<p><strong>Host:</strong> " . $http_host . "</p>";
echo "<p><strong>Protocol:</strong> " . $protocol . "</p>";
echo "<p><strong>Is Local:</strong> " . ($is_local ? 'Yes' : 'No') . "</p>";

if ($is_local) {
    echo "<p style='color: orange;'>⚠️ You are running this on localhost. This script is meant for live servers.</p>";
    exit;
}

// New URLs for live server
$new_home_url = $protocol . '://' . $http_host . '/wp';
$new_site_url = $protocol . '://' . $http_host . '/wp';

echo "<h2>New URLs:</h2>";
echo "<p><strong>WP_HOME:</strong> " . $new_home_url . "</p>";
echo "<p><strong>WP_SITEURL:</strong> " . $new_site_url . "</p>";

// Database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Database Connection:</h2>";
    echo "<p style='color: green;'>✅ Connected to database successfully!</p>";
    
    // Get table prefix
    $table_prefix = isset($table_prefix) ? $table_prefix : 'wp_';
    
    // Update siteurl and home options
    $updates = [
        'siteurl' => $new_site_url,
        'home' => $new_home_url
    ];
    
    echo "<h2>Updating WordPress Options:</h2>";
    
    foreach ($updates as $option_name => $option_value) {
        // Check current value
        $stmt = $pdo->prepare("SELECT option_value FROM {$table_prefix}options WHERE option_name = ?");
        $stmt->execute([$option_name]);
        $current = $stmt->fetchColumn();
        
        if ($current) {
            echo "<p><strong>$option_name:</strong></p>";
            echo "<p>Current: " . htmlspecialchars($current) . "</p>";
            echo "<p>New: " . htmlspecialchars($option_value) . "</p>";
            
            if ($current !== $option_value) {
                // Update the option
                $update_stmt = $pdo->prepare("UPDATE {$table_prefix}options SET option_value = ? WHERE option_name = ?");
                $update_stmt->execute([$option_value, $option_name]);
                echo "<p style='color: green;'>✅ Updated successfully!</p>";
            } else {
                echo "<p style='color: blue;'>ℹ️ Already correct, no update needed.</p>";
            }
        } else {
            // Insert new option
            $insert_stmt = $pdo->prepare("INSERT INTO {$table_prefix}options (option_name, option_value) VALUES (?, ?)");
            $insert_stmt->execute([$option_name, $option_value]);
            echo "<p style='color: green;'>✅ $option_name created with value: " . htmlspecialchars($option_value) . "</p>";
        }
        echo "<hr>";
    }
    
    // Update post content URLs (optional but recommended)
    echo "<h2>Updating Post Content URLs:</h2>";
    
    $old_url_patterns = [
        'http://localhost/loganixseo.com/wp',
        'http://localhost/loganixseo.com'
    ];
    
    $new_base_url = $protocol . '://' . $http_host;
    
    foreach ($old_url_patterns as $old_pattern) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$table_prefix}posts WHERE post_content LIKE ?");
        $stmt->execute(['%' . $old_pattern . '%']);
        $count = $stmt->fetchColumn();
        
        if ($count > 0) {
            echo "<p>Found $count posts with old URL pattern: $old_pattern</p>";
            
            // Update post content
            $replacement = ($old_pattern === 'http://localhost/loganixseo.com/wp') ? $new_base_url . '/wp' : $new_base_url;
            
            $update_stmt = $pdo->prepare("UPDATE {$table_prefix}posts SET post_content = REPLACE(post_content, ?, ?)");
            $update_stmt->execute([$old_pattern, $replacement]);
            
            echo "<p style='color: green;'>✅ Updated post content URLs from $old_pattern to $replacement</p>";
        }
    }
    
    echo "<h2>✅ WordPress URL Fix Complete!</h2>";
    echo "<p style='color: green;'>Your WordPress should now work properly on the live server.</p>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>Clear any caching plugins</li>";
    echo "<li>Update permalinks in WordPress admin</li>";
    echo "<li>Test your blog page: <a href='/pages/blog.php' target='_blank'>/pages/blog.php</a></li>";
    echo "<li>Delete this script file for security</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h2>❌ Database Error:</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database credentials in wp-config.php</p>";
}
?>
