<?php
require_once '../config/config.php';

$page_title = 'Word Counter - Free Text Word & Character Count Tool';
$page_description = 'Count words, characters, paragraphs, and sentences instantly with our free word counter tool. Get detailed text statistics and reading time estimates.';
$page_keywords = 'word counter, character counter, text counter, word count, character count, text statistics, reading time calculator, writing tool';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Word Counter",
        "description": "Count words, characters, paragraphs, and sentences instantly with our free word counter tool. Get detailed text statistics and reading time estimates.",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "Writing Tool",
        "operatingSystem": "Any",
        "permissions": "browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "SEO Agency",
            "url": "http://localhost/test"
        }
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main class="pt-20 lg:pt-24">
        <!-- Hero Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-8 lg:mb-10" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Word Counter
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-10 lg:mb-12" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Count words, characters, sentences, and paragraphs with detailed text analysis and statistics.
                    </p>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6"></div>

        <!-- Tool Section -->
        <section class="py-8 lg:py-12 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl">
                <div class="rounded-xl shadow-lg p-6 lg:p-8 border-2" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC, #F1F5F9); border-color: #22C55E;">
                    <div class="text-center mb-6 lg:mb-8">
                        <h2 class="text-2xl lg:text-3xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #22C55E, #16A34A); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Real-Time Text Analysis Tool</h2>
                        <p class="text-gray-700 text-base lg:text-lg font-medium">Get instant word count, character count, and comprehensive text statistics as you type</p>
                    </div>

                    <!-- Tool Interface -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                        <!-- Input Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Enter Your Text:</label>
                                <textarea id="textInput" rows="15" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 resize-none font-medium text-sm lg:text-base" style="border-color: #A7F3D0; background: linear-gradient(45deg, #ECFDF5, #D1FAE5);" placeholder="Start typing or paste your text here to get real-time word count and text statistics..."></textarea>
                            </div>

                            <!-- Quick Stats -->
                            <div class="grid grid-cols-2 gap-3 lg:gap-4">
                                <div class="p-3 lg:p-4 rounded-lg lg:rounded-xl text-center border-2 shadow-lg" style="background: linear-gradient(135deg, #EFF6FF, #DBEAFE); border-color: #3B82F6;">
                                    <div id="quickWords" class="text-xl lg:text-2xl font-bold text-blue-600">0</div>
                                    <div class="text-xs lg:text-sm text-gray-700 font-medium">Words</div>
                                </div>
                                <div class="p-3 lg:p-4 rounded-lg lg:rounded-xl text-center border-2 shadow-lg" style="background: linear-gradient(135deg, #F0FDF4, #DCFCE7); border-color: #22C55E;">
                                    <div id="quickChars" class="text-xl lg:text-2xl font-bold text-green-600">0</div>
                                    <div class="text-xs lg:text-sm text-gray-700 font-medium">Characters</div>
                                </div>
                            </div>

                            <!-- Options -->
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Count Options:</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="includeSpaces" checked class="mr-2 text-green-500">
                                        <span class="text-gray-700 font-medium text-sm">Include Spaces in Character Count</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="countPunctuation" checked class="mr-2 text-green-500">
                                        <span class="text-gray-700 font-medium text-sm">Count Punctuation as Characters</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="showReadingTime" checked class="mr-2 text-green-500">
                                        <span class="text-gray-700 font-medium text-sm">Show Reading Time Estimate</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <!-- Detailed Statistics -->
                            <div>
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #8B5CF6, #A855F7); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Detailed Statistics</h3>
                                <div class="grid grid-cols-2 gap-3 lg:gap-4">
                                    <div class="p-3 lg:p-4 rounded-lg lg:rounded-xl text-center border-2 shadow-lg" style="background: linear-gradient(135deg, #FAF5FF, #F3E8FF); border-color: #8B5CF6;">
                                        <div id="sentenceCount" class="text-xl lg:text-2xl font-bold text-purple-600">0</div>
                                        <div class="text-xs lg:text-sm text-gray-700 font-medium">Sentences</div>
                                    </div>
                                    <div class="p-3 lg:p-4 rounded-lg lg:rounded-xl text-center border-2 shadow-lg" style="background: linear-gradient(135deg, #FFF7ED, #FFEDD5); border-color: #F97316;">
                                        <div id="paragraphCount" class="text-xl lg:text-2xl font-bold text-orange-600">0</div>
                                        <div class="text-xs lg:text-sm text-gray-700 font-medium">Paragraphs</div>
                                    </div>
                                    <div class="p-3 lg:p-4 rounded-lg lg:rounded-xl text-center border-2 shadow-lg" style="background: linear-gradient(135deg, #ECFEFF, #CFFAFE); border-color: #06B6D4;">
                                        <div id="avgWordsPerSentence" class="text-xl lg:text-2xl font-bold text-cyan-600">0</div>
                                        <div class="text-xs lg:text-sm text-gray-700 font-medium">Avg Words/Sentence</div>
                                    </div>
                                    <div class="p-3 lg:p-4 rounded-lg lg:rounded-xl text-center border-2 shadow-lg" style="background: linear-gradient(135deg, #FDF2F8, #FCE7F3); border-color: #EC4899;">
                                        <div id="avgCharsPerWord" class="text-xl lg:text-2xl font-bold text-pink-600">0</div>
                                        <div class="text-xs lg:text-sm text-gray-700 font-medium">Avg Chars/Word</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Reading Time -->
                            <div id="readingTimeSection">
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4 text-gray-900">Reading Time</h3>
                                <div class="rounded-lg lg:rounded-xl p-4 lg:p-5 border-2 text-center" style="background: linear-gradient(135deg, #ECFDF5, #D1FAE5); border-color: #22C55E;">
                                    <div id="readingTime" class="text-3xl lg:text-4xl font-bold text-green-600 mb-2">0 min</div>
                                    <div class="text-sm lg:text-base text-gray-700 font-medium">Average Reading Time</div>
                                    <div class="text-xs text-gray-600 mt-2">Based on 200 words per minute</div>
                                </div>
                            </div>

                            <!-- Character Breakdown -->
                            <div>
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4 text-gray-900">Character Breakdown</h3>
                                <div class="rounded-lg lg:rounded-xl p-3 lg:p-4 border-2 text-sm" style="background: linear-gradient(135deg, #ECFDF5, #D1FAE5); border-color: #22C55E;">
                                    <div class="grid grid-cols-2 gap-2">
                                        <div class="flex justify-between">
                                            <span class="text-gray-700">With spaces:</span>
                                            <span id="charsWithSpaces" class="font-bold text-green-600">0</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-700">Without spaces:</span>
                                            <span id="charsWithoutSpaces" class="font-bold text-green-600">0</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-700">Letters only:</span>
                                            <span id="lettersOnly" class="font-bold text-green-600">0</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-700">Numbers:</span>
                                            <span id="numbersCount" class="font-bold text-green-600">0</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-700">Punctuation:</span>
                                            <span id="punctuationCount" class="font-bold text-green-600">0</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-700">Whitespace:</span>
                                            <span id="whitespaceCount" class="font-bold text-green-600">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Most Common Words -->
                            <div id="commonWordsSection">
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4 text-gray-900">Most Common Words</h3>
                                <div class="rounded-lg lg:rounded-xl p-3 lg:p-4 border-2 max-h-[150px] overflow-y-auto text-sm" style="background: linear-gradient(135deg, #ECFDF5, #D1FAE5); border-color: #22C55E;">
                                    <div id="commonWordsList" class="space-y-1">
                                        <div class="text-gray-500 text-center">Start typing to see common words...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 lg:mt-8 text-center">
                        <div class="flex flex-wrap justify-center gap-2 lg:gap-4">
                            <button onclick="clearText()" class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-300 text-sm lg:text-base">
                                Clear Text
                            </button>
                            <button onclick="loadSample()" class="px-4 py-2 text-green-600 hover:text-green-800 border border-green-300 rounded-lg hover:bg-green-50 transition-all duration-300 text-sm lg:text-base">
                                Load Sample
                            </button>
                            <button onclick="copyStats()" class="px-4 py-2 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-lg hover:bg-blue-50 transition-all duration-300 text-sm lg:text-base">
                                Copy Statistics
                            </button>
                            <button onclick="exportReport()" class="px-4 py-2 text-purple-600 hover:text-purple-800 border border-purple-300 rounded-lg hover:bg-purple-50 transition-all duration-300 text-sm lg:text-base">
                                Export Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>

    <script>
        let currentStats = {};

        // Real-time text analysis
        document.getElementById('textInput').addEventListener('input', function() {
            analyzeText();
        });

        // Option change listeners
        document.getElementById('includeSpaces').addEventListener('change', analyzeText);
        document.getElementById('countPunctuation').addEventListener('change', analyzeText);
        document.getElementById('showReadingTime').addEventListener('change', analyzeText);

        function analyzeText() {
            const text = document.getElementById('textInput').value;
            const includeSpaces = document.getElementById('includeSpaces').checked;
            const countPunctuation = document.getElementById('countPunctuation').checked;
            const showReadingTime = document.getElementById('showReadingTime').checked;

            // Calculate statistics
            currentStats = calculateTextStats(text, {
                includeSpaces,
                countPunctuation,
                showReadingTime
            });

            // Update display
            updateDisplay(currentStats);
        }

        function calculateTextStats(text, options) {
            const stats = {};

            // Basic counts
            stats.characters = text.length;
            stats.charactersNoSpaces = text.replace(/\s/g, '').length;
            stats.words = text.trim() ? text.trim().split(/\s+/).length : 0;
            stats.sentences = text.trim() ? text.split(/[.!?]+/).filter(s => s.trim().length > 0).length : 0;
            stats.paragraphs = text.trim() ? text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length : 0;

            // Character breakdown
            stats.lettersOnly = (text.match(/[a-zA-Z]/g) || []).length;
            stats.numbers = (text.match(/[0-9]/g) || []).length;
            stats.punctuation = (text.match(/[^\w\s]/g) || []).length;
            stats.whitespace = (text.match(/\s/g) || []).length;

            // Averages
            stats.avgWordsPerSentence = stats.sentences > 0 ? (stats.words / stats.sentences).toFixed(1) : 0;
            stats.avgCharsPerWord = stats.words > 0 ? (stats.charactersNoSpaces / stats.words).toFixed(1) : 0;

            // Reading time (200 words per minute average)
            stats.readingTimeMinutes = Math.ceil(stats.words / 200);
            stats.readingTimeSeconds = Math.ceil((stats.words / 200) * 60);

            // Most common words
            stats.commonWords = getCommonWords(text);

            return stats;
        }

        function getCommonWords(text) {
            if (!text.trim()) return [];

            const words = text.toLowerCase()
                .replace(/[^\w\s]/g, '')
                .split(/\s+/)
                .filter(word => word.length > 2);

            const wordCount = {};
            words.forEach(word => {
                wordCount[word] = (wordCount[word] || 0) + 1;
            });

            return Object.entries(wordCount)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10)
                .map(([word, count]) => ({ word, count }));
        }

        function updateDisplay(stats) {
            // Quick stats
            document.getElementById('quickWords').textContent = stats.words.toLocaleString();
            document.getElementById('quickChars').textContent = stats.characters.toLocaleString();

            // Detailed statistics
            document.getElementById('sentenceCount').textContent = stats.sentences.toLocaleString();
            document.getElementById('paragraphCount').textContent = stats.paragraphs.toLocaleString();
            document.getElementById('avgWordsPerSentence').textContent = stats.avgWordsPerSentence;
            document.getElementById('avgCharsPerWord').textContent = stats.avgCharsPerWord;

            // Character breakdown
            document.getElementById('charsWithSpaces').textContent = stats.characters.toLocaleString();
            document.getElementById('charsWithoutSpaces').textContent = stats.charactersNoSpaces.toLocaleString();
            document.getElementById('lettersOnly').textContent = stats.lettersOnly.toLocaleString();
            document.getElementById('numbersCount').textContent = stats.numbers.toLocaleString();
            document.getElementById('punctuationCount').textContent = stats.punctuation.toLocaleString();
            document.getElementById('whitespaceCount').textContent = stats.whitespace.toLocaleString();

            // Reading time
            const readingTimeText = stats.readingTimeMinutes > 0 ? 
                `${stats.readingTimeMinutes} min` : 
                `${stats.readingTimeSeconds} sec`;
            document.getElementById('readingTime').textContent = readingTimeText;

            // Common words
            updateCommonWords(stats.commonWords);
        }

        function updateCommonWords(commonWords) {
            const commonWordsList = document.getElementById('commonWordsList');
            
            if (commonWords.length === 0) {
                commonWordsList.innerHTML = '<div class="text-gray-500 text-center">Start typing to see common words...</div>';
                return;
            }

            commonWordsList.innerHTML = '';
            commonWords.forEach(({ word, count }) => {
                const div = document.createElement('div');
                div.className = 'flex justify-between items-center';
                div.innerHTML = `
                    <span class="text-gray-700">${word}</span>
                    <span class="font-bold text-green-600">${count}</span>
                `;
                commonWordsList.appendChild(div);
            });
        }

        function copyStats() {
            if (!currentStats.words) {
                alert('No statistics to copy. Please enter some text first.');
                return;
            }

            const statsText = `Text Statistics:
Words: ${currentStats.words}
Characters (with spaces): ${currentStats.characters}
Characters (without spaces): ${currentStats.charactersNoSpaces}
Sentences: ${currentStats.sentences}
Paragraphs: ${currentStats.paragraphs}
Average words per sentence: ${currentStats.avgWordsPerSentence}
Average characters per word: ${currentStats.avgCharsPerWord}
Reading time: ${currentStats.readingTimeMinutes} minutes`;

            navigator.clipboard.writeText(statsText).then(() => {
                alert('Statistics copied to clipboard!');
            }).catch(() => {
                alert('Statistics copied to clipboard!');
            });
        }

        function exportReport() {
            if (!currentStats.words) {
                alert('No statistics to export. Please enter some text first.');
                return;
            }

            const text = document.getElementById('textInput').value;
            const report = `Text Analysis Report
Generated: ${new Date().toLocaleString()}

TEXT STATISTICS:
Words: ${currentStats.words}
Characters (with spaces): ${currentStats.characters}
Characters (without spaces): ${currentStats.charactersNoSpaces}
Sentences: ${currentStats.sentences}
Paragraphs: ${currentStats.paragraphs}

AVERAGES:
Words per sentence: ${currentStats.avgWordsPerSentence}
Characters per word: ${currentStats.avgCharsPerWord}

CHARACTER BREAKDOWN:
Letters: ${currentStats.lettersOnly}
Numbers: ${currentStats.numbers}
Punctuation: ${currentStats.punctuation}
Whitespace: ${currentStats.whitespace}

READING TIME:
Estimated reading time: ${currentStats.readingTimeMinutes} minutes

MOST COMMON WORDS:
${currentStats.commonWords.map(({ word, count }) => `${word}: ${count}`).join('\n')}

ORIGINAL TEXT:
${text}`;

            const blob = new Blob([report], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'text-analysis-report.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function clearText() {
            document.getElementById('textInput').value = '';
            analyzeText();
        }

        function loadSample() {
            const sampleText = `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.`;
            
            document.getElementById('textInput').value = sampleText;
            analyzeText();
        }

        // Initialize
        analyzeText();
    </script>
</body>
</html>
