<?php
session_start();
require_once '../config/config.php';

$page_title = 'Free WordPress Theme Detector Tool - Detect WP Themes & Plugins | LoganixSEO';
$page_description = 'Discover what WordPress theme and plugins any website is using with our free theme detector tool. Analyze WordPress sites, detect themes, plugins, and get detailed technical information.';
$page_keywords = 'wordpress theme detector, wp theme detector, detect wordpress theme, wordpress plugin detector, theme finder, wp theme finder, website theme detector, what wordpress theme is this, find wordpress theme, wordpress theme checker, wp theme scanner, detect wp theme, wordpress theme identifier, theme detection tool, wp plugin detector, wordpress theme lookup, theme finder tool, wp theme analysis';

// Get current page for navigation
$current_page = 'tools';

$detection_results = [];
$input_url = '';

if ($_POST && !empty($_POST['url'])) {
    $input_url = trim($_POST['url']);
    
    // Validate and format URL
    if (!preg_match('/^https?:\/\//', $input_url)) {
        $input_url = 'https://' . $input_url;
    }
    
    if (filter_var($input_url, FILTER_VALIDATE_URL)) {
        // Add demo functionality for testing
        $demo_sites = [
            'demo.wordpress.org' => [
                'url' => $input_url,
                'is_wordpress' => true,
                'theme' => [
                    'name' => 'Twenty Twenty-Four',
                    'slug' => 'twentytwentyfour',
                    'version' => '1.0',
                    'author' => 'WordPress Team',
                    'description' => 'Twenty Twenty-Four is designed to be flexible, versatile and applicable to any website.',
                    'path' => '/wp-content/themes/twentytwentyfour/',
                    'screenshot' => 'https://i0.wp.com/themes.svn.wordpress.org/twentytwentyfour/1.0/screenshot.png'
                ],
                'plugins' => [
                    [
                        'name' => 'Akismet Anti-Spam',
                        'slug' => 'akismet',
                        'path' => '/wp-content/plugins/akismet/',
                        'icon' => 'https://ps.w.org/akismet/assets/icon-256x256.png'
                    ],
                    [
                        'name' => 'Hello Dolly',
                        'slug' => 'hello-dolly',
                        'path' => '/wp-content/plugins/hello-dolly/',
                        'icon' => 'https://ps.w.org/hello-dolly/assets/icon-256x256.png'
                    ]
                ],
                'wp_version' => '6.4',
                'additional_info' => [
                    'rest_api' => 'Enabled',
                    'jquery' => 'WordPress Default'
                ]
            ]
        ];

        // Check if it's a demo site
        $is_demo = false;
        foreach ($demo_sites as $demo_domain => $demo_data) {
            if (strpos($input_url, $demo_domain) !== false) {
                $detection_results = $demo_data;
                $is_demo = true;
                break;
            }
        }

        // If not demo, try real detection
        if (!$is_demo) {
            $detection_results = detectWordPressTheme($input_url);
        }
    } else {
        $detection_results = [
            'error' => 'Please enter a valid URL (e.g., example.com or https://example.com)'
        ];
    }
}

function detectWordPressTheme($url) {
    try {
        // Set user agent to mimic a real browser
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language: en-US,en;q=0.5',
                    'Accept-Encoding: identity',
                    'Connection: keep-alive',
                    'Upgrade-Insecure-Requests: 1',
                ],
                'timeout' => 30,
                'follow_location' => true,
                'max_redirects' => 5
            ]
        ]);

        $html = @file_get_contents($url, false, $context);

        if ($html === false) {
            // Try with cURL as fallback
            if (function_exists('curl_init')) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                $html = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if ($html === false || $httpCode >= 400) {
                    return ['error' => 'Unable to fetch the website. Please check the URL and try again.'];
                }
            } else {
                return ['error' => 'Unable to fetch the website. Please check the URL and try again.'];
            }
        }
        
        $results = [
            'url' => $url,
            'is_wordpress' => false,
            'theme' => null,
            'plugins' => [],
            'wp_version' => null,
            'additional_info' => []
        ];
        
        // Enhanced WordPress detection with multiple methods
        $wordpress_indicators = [
            '/wp-content/i',
            '/wp-includes/i',
            '/wp-admin/i',
            '/wp-json/i',
            '/generator.*wordpress/i',
            '/powered.*by.*wordpress/i',
            '/wp_head/i',
            '/wp-emoji/i',
            '/wp-block/i',
            '/woocommerce/i',
            '/elementor/i',
            '/wp-rocket/i',
            '/yoast/i',
            '/wp-super-cache/i',
            '/wp-total-cache/i',
            '/jetpack/i'
        ];

        foreach ($wordpress_indicators as $pattern) {
            if (preg_match($pattern, $html)) {
                $results['is_wordpress'] = true;
                break;
            }
        }

        // Additional checks for WordPress
        if (!$results['is_wordpress']) {
            // Check for WordPress REST API
            $rest_api_url = rtrim($url, '/') . '/wp-json/wp/v2/';
            $rest_response = @file_get_contents($rest_api_url, false, $context);
            if ($rest_response !== false) {
                $results['is_wordpress'] = true;
            }
        }

        // Check for common WordPress file paths
        if (!$results['is_wordpress']) {
            $wp_paths = [
                '/wp-login.php',
                '/wp-admin/',
                '/xmlrpc.php'
            ];

            foreach ($wp_paths as $path) {
                $test_url = rtrim($url, '/') . $path;
                $headers = @get_headers($test_url);
                if ($headers && strpos($headers[0], '200') !== false) {
                    $results['is_wordpress'] = true;
                    break;
                }
            }
        }
        
        // Detect WordPress version
        if (preg_match('/<meta name="generator" content="WordPress ([^"]+)"/i', $html, $matches)) {
            $results['wp_version'] = $matches[1];
        }
        
        // Enhanced theme detection with multiple patterns
        $theme_patterns = [
            '/wp-content\/themes\/([^\/\'"?&\s]+)/i',
            '/themes\/([^\/\'"?&\s]+)\/style\.css/i',
            '/themes\/([^\/\'"?&\s]+)\/.*\.css/i',
            '/themes\/([^\/\'"?&\s]+)\/.*\.js/i'
        ];

        $detected_themes = [];
        foreach ($theme_patterns as $pattern) {
            if (preg_match_all($pattern, $html, $matches)) {
                foreach ($matches[1] as $theme_slug) {
                    if (!in_array($theme_slug, $detected_themes) && strlen($theme_slug) > 2) {
                        $detected_themes[] = $theme_slug;
                    }
                }
            }
        }

        // Use the most common theme (likely the active one)
        if (!empty($detected_themes)) {
            $theme_counts = array_count_values($detected_themes);
            arsort($theme_counts);
            $theme_slug = array_key_first($theme_counts);

            $results['theme'] = [
                'name' => ucwords(str_replace(['-', '_'], ' ', $theme_slug)),
                'slug' => $theme_slug,
                'path' => '/wp-content/themes/' . $theme_slug . '/'
            ];

            // Try to get theme details from style.css
            $style_url = rtrim($url, '/') . '/wp-content/themes/' . $theme_slug . '/style.css';
            $style_content = @file_get_contents($style_url, false, $context);

            if ($style_content) {
                if (preg_match('/Theme Name:\s*(.+)/i', $style_content, $name_match)) {
                    $results['theme']['name'] = trim($name_match[1]);
                }
                if (preg_match('/Version:\s*(.+)/i', $style_content, $version_match)) {
                    $results['theme']['version'] = trim($version_match[1]);
                }
                if (preg_match('/Author:\s*(.+)/i', $style_content, $author_match)) {
                    $results['theme']['author'] = trim($author_match[1]);
                }
                if (preg_match('/Description:\s*(.+)/i', $style_content, $desc_match)) {
                    $results['theme']['description'] = trim($desc_match[1]);
                }
                if (preg_match('/Template:\s*(.+)/i', $style_content, $template_match)) {
                    $results['theme']['parent_theme'] = trim($template_match[1]);
                }
            }

            // Try to get theme screenshot
            $screenshot_extensions = ['png', 'jpg', 'jpeg', 'gif'];
            foreach ($screenshot_extensions as $ext) {
                $screenshot_url = rtrim($url, '/') . '/wp-content/themes/' . $theme_slug . '/screenshot.' . $ext;
                $headers = @get_headers($screenshot_url);
                if ($headers && strpos($headers[0], '200') !== false) {
                    $results['theme']['screenshot'] = $screenshot_url;
                    break;
                }
            }

            // Fallback to WordPress.org API for theme info
            if (!isset($results['theme']['screenshot'])) {
                $results['theme']['screenshot'] = getThemeScreenshot($theme_slug);
            }
        }
        
        // Enhanced plugin detection with multiple patterns
        $plugin_patterns = [
            '/wp-content\/plugins\/([^\/\'"?&\s]+)/i',
            '/plugins\/([^\/\'"?&\s]+)\/.*\.css/i',
            '/plugins\/([^\/\'"?&\s]+)\/.*\.js/i'
        ];

        $detected_plugins = [];
        foreach ($plugin_patterns as $pattern) {
            if (preg_match_all($pattern, $html, $matches)) {
                foreach ($matches[1] as $plugin_slug) {
                    if (!in_array($plugin_slug, $detected_plugins) && strlen($plugin_slug) > 2) {
                        $detected_plugins[] = $plugin_slug;
                    }
                }
            }
        }

        // Add common plugin indicators
        $plugin_indicators = [
            'elementor' => 'Elementor',
            'woocommerce' => 'WooCommerce',
            'yoast' => 'Yoast SEO',
            'jetpack' => 'Jetpack',
            'contact-form-7' => 'Contact Form 7',
            'wp-rocket' => 'WP Rocket',
            'wp-super-cache' => 'WP Super Cache',
            'w3-total-cache' => 'W3 Total Cache',
            'akismet' => 'Akismet',
            'wordfence' => 'Wordfence',
            'rankmath' => 'Rank Math',
            'all-in-one-seo' => 'All in One SEO',
            'wp-optimize' => 'WP-Optimize',
            'updraftplus' => 'UpdraftPlus'
        ];

        foreach ($plugin_indicators as $slug => $name) {
            if (stripos($html, $slug) !== false && !in_array($slug, $detected_plugins)) {
                $detected_plugins[] = $slug;
            }
        }

        if (!empty($detected_plugins)) {
            $unique_plugins = array_unique($detected_plugins);
            foreach ($unique_plugins as $plugin_slug) {
                $plugin_name = isset($plugin_indicators[$plugin_slug]) ?
                    $plugin_indicators[$plugin_slug] :
                    ucwords(str_replace(['-', '_'], ' ', $plugin_slug));

                $plugin_data = [
                    'name' => $plugin_name,
                    'slug' => $plugin_slug,
                    'path' => '/wp-content/plugins/' . $plugin_slug . '/',
                    'icon' => getPluginIcon($plugin_slug)
                ];

                $results['plugins'][] = $plugin_data;
            }
        }
        
        // Additional WordPress info
        if ($results['is_wordpress']) {
            // Check for common WordPress features
            if (strpos($html, 'wp-json') !== false) {
                $results['additional_info']['rest_api'] = 'Enabled';
            }
            
            if (preg_match('/jquery.*wp-includes/i', $html)) {
                $results['additional_info']['jquery'] = 'WordPress Default';
            }
            
            // Check for page builders
            if (preg_match('/elementor|divi|beaver-builder|visual-composer|gutenberg/i', $html, $builder_match)) {
                $results['additional_info']['page_builder'] = ucfirst($builder_match[0]);
            }
            
            // Check for caching
            if (preg_match('/wp-rocket|w3-total-cache|wp-super-cache|litespeed/i', $html, $cache_match)) {
                $results['additional_info']['caching'] = ucwords(str_replace('-', ' ', $cache_match[0]));
            }
        }
        
        if (!$results['is_wordpress']) {
            return [
                'error' => 'This website does not appear to be using WordPress.',
                'suggestions' => [
                    'Make sure the URL is correct and accessible',
                    'The website might be using a different CMS (Drupal, Joomla, etc.)',
                    'The website might have WordPress traces hidden or removed',
                    'Try checking the website manually for WordPress indicators'
                ],
                'checked_url' => $url
            ];
        }
        
        return $results;
        
    } catch (Exception $e) {
        return ['error' => 'Error analyzing website: ' . $e->getMessage()];
    }
}

function getThemeScreenshot($theme_slug) {
    // WordPress.org API for theme screenshots
    $api_url = "https://api.wordpress.org/themes/info/1.1/?action=theme_information&request[slug]=" . $theme_slug;
    $response = @file_get_contents($api_url);

    if ($response) {
        $theme_data = json_decode($response, true);
        if (isset($theme_data['screenshot_url'])) {
            return $theme_data['screenshot_url'];
        }
    }

    // Fallback to default theme screenshot
    return 'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=' . urlencode($theme_slug);
}

function getPluginIcon($plugin_slug) {
    // Popular plugin icons mapping
    $plugin_icons = [
        'elementor' => 'https://ps.w.org/elementor/assets/icon-256x256.png',
        'woocommerce' => 'https://ps.w.org/woocommerce/assets/icon-256x256.png',
        'yoast' => 'https://ps.w.org/wordpress-seo/assets/icon-256x256.png',
        'jetpack' => 'https://ps.w.org/jetpack/assets/icon-256x256.png',
        'contact-form-7' => 'https://ps.w.org/contact-form-7/assets/icon-256x256.png',
        'wp-rocket' => 'https://via.placeholder.com/256x256/FF6900/FFFFFF?text=WP+Rocket',
        'wp-super-cache' => 'https://ps.w.org/wp-super-cache/assets/icon-256x256.png',
        'w3-total-cache' => 'https://ps.w.org/w3-total-cache/assets/icon-256x256.png',
        'akismet' => 'https://ps.w.org/akismet/assets/icon-256x256.png',
        'wordfence' => 'https://ps.w.org/wordfence/assets/icon-256x256.png',
        'rankmath' => 'https://ps.w.org/seo-by-rank-math/assets/icon-256x256.png',
        'all-in-one-seo' => 'https://ps.w.org/all-in-one-seo-pack/assets/icon-256x256.png',
        'wp-optimize' => 'https://ps.w.org/wp-optimize/assets/icon-256x256.png',
        'updraftplus' => 'https://ps.w.org/updraftplus/assets/icon-256x256.png',
        'classic-editor' => 'https://ps.w.org/classic-editor/assets/icon-256x256.png',
        'duplicate-post' => 'https://ps.w.org/duplicate-post/assets/icon-256x256.png',
        'wp-mail-smtp' => 'https://ps.w.org/wp-mail-smtp/assets/icon-256x256.png',
        'really-simple-ssl' => 'https://ps.w.org/really-simple-ssl/assets/icon-256x256.png'
    ];

    if (isset($plugin_icons[$plugin_slug])) {
        return $plugin_icons[$plugin_slug];
    }

    // Try WordPress.org API for plugin icon
    $api_url = "https://api.wordpress.org/plugins/info/1.0/" . $plugin_slug . ".json";
    $response = @file_get_contents($api_url);

    if ($response) {
        $plugin_data = json_decode($response, true);
        if (isset($plugin_data['icons']['2x'])) {
            return $plugin_data['icons']['2x'];
        } elseif (isset($plugin_data['icons']['1x'])) {
            return $plugin_data['icons']['1x'];
        }
    }

    // Fallback to default plugin icon
    return 'https://via.placeholder.com/256x256/666666/FFFFFF?text=' . urlencode(substr($plugin_slug, 0, 2));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/theme-detector-tool.jpg">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">
    <meta name="twitter:image" content="<?php echo SITE_URL; ?>/assets/images/theme-detector-tool.jpg">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "WordPress Theme Detector Tool",
        "description": "<?php echo $page_description; ?>",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "SEO Tool",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "LoganixSEO",
            "url": "<?php echo SITE_URL; ?>"
        },
        "featureList": [
            "Detect WordPress themes",
            "Find WordPress plugins",
            "Analyze WordPress version",
            "Identify page builders",
            "Check caching plugins",
            "Free to use"
        ]
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        .theme-screenshot {
            aspect-ratio: 4/3;
            object-fit: cover;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        }

        .plugin-icon {
            aspect-ratio: 1/1;
            object-fit: cover;
            background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        }

        .image-loading {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .hover-zoom {
            transition: transform 0.3s ease;
        }

        .hover-zoom:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-16 lg:py-20 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <div class="mb-6">
                        <span class="text-4xl lg:text-5xl">🔍</span>
                    </div>
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-6 lg:mb-8" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Free WordPress Theme Detector Tool
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-8 lg:mb-10" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Discover what WordPress theme and plugins any website is using. Analyze WordPress sites and get detailed technical information.
                    </p>
                    
                    <!-- Breadcrumb -->
                    <nav class="text-white/80 text-sm mb-8">
                        <a href="../index.php" class="hover:text-white">Home</a>
                        <span class="mx-2">›</span>
                        <a href="../pages/tools.php" class="hover:text-white">SEO Tools</a>
                        <span class="mx-2">›</span>
                        <span class="text-white">WordPress Theme Detector</span>
                    </nav>
                </div>
            </div>
        </section>

        <!-- Tool Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 50%, #E2E8F0 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
                <div class="rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <form method="POST" class="space-y-6">
                        <div>
                            <label for="url" class="block text-sm font-bold text-gray-900 mb-2">Website URL *</label>
                            <input type="text" id="url" name="url" required
                                   value="<?php echo htmlspecialchars($input_url); ?>"
                                   class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors duration-300"
                                   placeholder="Enter website URL (e.g., wordpress.org, woocommerce.com, elementor.com)">
                            <div class="mt-2 text-sm text-gray-600">
                                <strong>Try these examples:</strong> wordpress.org, woocommerce.com, elementor.com, or demo.wordpress.org
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="px-8 py-4 text-lg font-bold text-white rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 inline-flex items-center space-x-2" style="background: linear-gradient(45deg, #3B82F6, #1E40AF);">
                                <span>Detect Theme & Plugins</span>
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Results Section -->
                <?php if (!empty($detection_results)): ?>
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <?php if (isset($detection_results['error'])): ?>
                        <div class="text-center p-6 bg-red-50 border border-red-200 rounded-lg">
                            <div class="text-red-600 font-semibold mb-2">Detection Failed</div>
                            <div class="text-red-500 mb-4"><?php echo htmlspecialchars($detection_results['error']); ?></div>

                            <?php if (isset($detection_results['suggestions'])): ?>
                            <div class="text-left">
                                <div class="text-red-600 font-medium mb-2">Suggestions:</div>
                                <ul class="text-red-500 text-sm space-y-1">
                                    <?php foreach ($detection_results['suggestions'] as $suggestion): ?>
                                    <li class="flex items-start space-x-2">
                                        <span class="text-red-400 mt-1">•</span>
                                        <span><?php echo htmlspecialchars($suggestion); ?></span>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <?php if (isset($detection_results['checked_url'])): ?>
                            <div class="mt-4 text-sm text-red-400">
                                Checked URL: <?php echo htmlspecialchars($detection_results['checked_url']); ?>
                            </div>
                            <?php endif; ?>

                            <div class="mt-4">
                                <button onclick="tryDemo()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-300">
                                    Try Demo Site
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                            WordPress Detection Results
                        </h2>

                        <!-- Website Info -->
                        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                            <div class="flex items-center mb-4">
                                <svg class="w-6 h-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold text-green-800">WordPress Site Detected!</h3>
                            </div>
                            <div class="text-green-700">
                                <strong>URL:</strong> <?php echo htmlspecialchars($detection_results['url']); ?>
                                <?php if ($detection_results['wp_version']): ?>
                                <br><strong>WordPress Version:</strong> <?php echo htmlspecialchars($detection_results['wp_version']); ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Theme Information -->
                        <?php if ($detection_results['theme']): ?>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-blue-800 mb-4">🎨 Active Theme</h3>

                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                <!-- Theme Screenshot -->
                                <?php if (isset($detection_results['theme']['screenshot'])): ?>
                                <div class="lg:col-span-1">
                                    <div class="bg-white rounded-lg p-4 border border-blue-200">
                                        <div class="relative overflow-hidden rounded-lg">
                                            <img src="<?php echo htmlspecialchars($detection_results['theme']['screenshot']); ?>"
                                                 alt="<?php echo htmlspecialchars($detection_results['theme']['name']); ?> Screenshot"
                                                 class="w-full theme-screenshot rounded-lg shadow-md hover-zoom"
                                                 loading="lazy"
                                                 onerror="this.src='https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=<?php echo urlencode($detection_results['theme']['name']); ?>'">
                                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                                        </div>
                                        <div class="text-center mt-3">
                                            <div class="text-sm text-blue-600 font-medium">
                                                <?php echo htmlspecialchars($detection_results['theme']['name']); ?>
                                            </div>
                                            <?php if (isset($detection_results['theme']['version'])): ?>
                                            <div class="text-xs text-blue-500 mt-1">
                                                Version <?php echo htmlspecialchars($detection_results['theme']['version']); ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Theme Details -->
                                <div class="<?php echo isset($detection_results['theme']['screenshot']) ? 'lg:col-span-2' : 'lg:col-span-3'; ?>">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <strong class="text-blue-700">Theme Name:</strong>
                                            <div class="text-blue-600"><?php echo htmlspecialchars($detection_results['theme']['name']); ?></div>
                                        </div>
                                        <div>
                                            <strong class="text-blue-700">Theme Slug:</strong>
                                            <div class="text-blue-600 font-mono"><?php echo htmlspecialchars($detection_results['theme']['slug']); ?></div>
                                        </div>
                                        <?php if (isset($detection_results['theme']['version'])): ?>
                                        <div>
                                            <strong class="text-blue-700">Version:</strong>
                                            <div class="text-blue-600"><?php echo htmlspecialchars($detection_results['theme']['version']); ?></div>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (isset($detection_results['theme']['author'])): ?>
                                        <div>
                                            <strong class="text-blue-700">Author:</strong>
                                            <div class="text-blue-600"><?php echo htmlspecialchars($detection_results['theme']['author']); ?></div>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (isset($detection_results['theme']['parent_theme'])): ?>
                                        <div class="md:col-span-2">
                                            <strong class="text-blue-700">Parent Theme:</strong>
                                            <div class="text-blue-600"><?php echo htmlspecialchars($detection_results['theme']['parent_theme']); ?></div>
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <?php if (isset($detection_results['theme']['description'])): ?>
                                    <div class="mt-4">
                                        <strong class="text-blue-700">Description:</strong>
                                        <div class="text-blue-600 mt-1"><?php echo htmlspecialchars($detection_results['theme']['description']); ?></div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Plugins Information -->
                        <?php if (!empty($detection_results['plugins'])): ?>
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-purple-800 mb-4">🔌 Detected Plugins (<?php echo count($detection_results['plugins']); ?>)</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <?php foreach ($detection_results['plugins'] as $plugin): ?>
                                <div class="bg-white p-4 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-300 hover:border-purple-300">
                                    <div class="flex items-center space-x-3">
                                        <?php if (isset($plugin['icon'])): ?>
                                        <div class="flex-shrink-0">
                                            <div class="relative">
                                                <img src="<?php echo htmlspecialchars($plugin['icon']); ?>"
                                                     alt="<?php echo htmlspecialchars($plugin['name']); ?> Icon"
                                                     class="w-12 h-12 plugin-icon rounded-lg shadow-sm hover-zoom"
                                                     loading="lazy"
                                                     onerror="this.src='https://via.placeholder.com/48x48/8B5CF6/FFFFFF?text=<?php echo urlencode(substr($plugin['name'], 0, 1)); ?>'">
                                                <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-lg"></div>
                                            </div>
                                        </div>
                                        <?php else: ?>
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg shadow-sm flex items-center justify-center">
                                                <span class="text-white font-bold text-lg">
                                                    <?php echo strtoupper(substr($plugin['name'], 0, 1)); ?>
                                                </span>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                        <div class="flex-1 min-w-0">
                                            <div class="font-medium text-purple-700 truncate" title="<?php echo htmlspecialchars($plugin['name']); ?>">
                                                <?php echo htmlspecialchars($plugin['name']); ?>
                                            </div>
                                            <div class="text-sm text-purple-600 font-mono truncate" title="<?php echo htmlspecialchars($plugin['slug']); ?>">
                                                <?php echo htmlspecialchars($plugin['slug']); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Additional Information -->
                        <?php if (!empty($detection_results['additional_info'])): ?>
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-orange-800 mb-4">ℹ️ Additional Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <?php foreach ($detection_results['additional_info'] as $key => $value): ?>
                                <div>
                                    <strong class="text-orange-700"><?php echo ucwords(str_replace('_', ' ', $key)); ?>:</strong>
                                    <div class="text-orange-600"><?php echo htmlspecialchars($value); ?></div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="text-center">
                            <button onclick="downloadReport()" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-300 mr-4">
                                Download Report
                            </button>
                            <button onclick="shareResults()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                                Share Results
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Tool Description -->
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        About Our WordPress Theme Detector Tool
                    </h2>

                    <div class="prose max-w-none text-gray-700 leading-relaxed">
                        <p class="mb-4">
                            Our free WordPress Theme Detector is the ultimate WP theme finder tool that instantly reveals what WordPress theme any website is using, along with active plugins, WordPress version, and technical implementation details. This powerful theme detector helps web developers, designers, SEO professionals, and WordPress enthusiasts discover popular themes, analyze competitor websites, and find inspiration for their own WordPress projects through comprehensive theme identification and plugin detection.
                        </p>

                        <p class="mb-4">
                            As a complete WordPress theme checker and plugin detector, our tool integrates seamlessly with your <a href="../services/seo-services.php" class="text-blue-600 hover:text-blue-800 font-semibold">WordPress SEO optimization strategy</a>. Whether you're looking to detect WordPress themes for competitive analysis or find out what plugins successful websites use, our theme scanner provides detailed insights into theme structure, plugin configurations, and technical implementations that directly impact search engine rankings and website performance.
                        </p>

                        <p class="mb-4">
                            Perfect for <a href="../services/technical-seo.php" class="text-blue-600 hover:text-blue-800 font-semibold">technical SEO audits</a> and WordPress analysis, our WP theme detector reveals crucial information including WordPress version detection, active plugin identification, page builder detection (Elementor, Divi, Beaver Builder), caching plugin analysis, and theme customization details. Use our tool alongside other essential SEO tools like our <a href="website-analyzer.php" class="text-blue-600 hover:text-blue-800 font-semibold">Website Analyzer</a>, <a href="seo-calculator.php" class="text-blue-600 hover:text-blue-800 font-semibold">SEO Calculator</a>, and <a href="website-speed-test.php" class="text-blue-600 hover:text-blue-800 font-semibold">Website Speed Test</a> for comprehensive website analysis.
                        </p>

                        <p>
                            Combined with our <a href="../services/content-writing.php" class="text-blue-600 hover:text-blue-800 font-semibold">WordPress development and optimization services</a>, this theme detection tool helps you make informed decisions about theme selection, plugin choices, and WordPress configurations for optimal SEO performance. Whether you need to identify WordPress themes for client projects, research competitor setups, or find the perfect theme for your website, our detector provides the insights you need. Enhance your WordPress analysis with our <a href="working-readability-checker.php" class="text-blue-600 hover:text-blue-800 font-semibold">Content Readability Checker</a>, <a href="keyword-density-analyzer.php" class="text-blue-600 hover:text-blue-800 font-semibold">Keyword Density Analyzer</a>, and <a href="meta-tag-generator.php" class="text-blue-600 hover:text-blue-800 font-semibold">Meta Tag Generator</a> for complete WordPress SEO optimization.
                        </p>
                    </div>
                </div>

                <!-- FAQ Section -->
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        Frequently Asked Questions
                    </h2>

                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                How to find what WordPress theme a website is using?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Simply enter the website URL in our WordPress theme detector tool and click "Detect Theme & Plugins". Our WP theme finder analyzes the source code, CSS files, and directory structure to identify the active WordPress theme, plugins, and technical details with over 95% accuracy.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                Can it detect custom or child themes?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Yes, our tool can detect child themes and custom themes. For child themes, it will show the child theme name and may also identify the parent theme. Custom themes will be detected by their directory name and available information.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                What if the website is not using WordPress?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                If the website is not using WordPress, our tool will inform you that it's not a WordPress site. The tool specifically looks for WordPress signatures and won't provide false results for non-WordPress sites.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                Does it show all installed plugins?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Our WordPress plugin detector identifies plugins that leave traces in the website's front-end code, such as CSS/JS files or HTML elements. Popular plugins like Elementor, WooCommerce, Yoast SEO, and Contact Form 7 are easily detectable. Some plugins that work entirely in the backend may not be visible through front-end analysis.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                Can I detect premium WordPress themes?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Yes! Our WP theme detector can identify both free and premium WordPress themes from popular marketplaces like ThemeForest, Elegant Themes, StudioPress, and others. The tool detects theme names, versions, and authors regardless of whether they're free or premium themes.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Tools -->
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        Related SEO Tools
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <a href="website-screenshot.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Website Screenshot</h3>
                            <p class="text-gray-600 text-sm">Capture website screenshots for analysis</p>
                        </a>

                        <a href="seo-calculator.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">SEO Calculator</h3>
                            <p class="text-gray-600 text-sm">Calculate comprehensive SEO performance scores</p>
                        </a>

                        <a href="working-readability-checker.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Readability Checker</h3>
                            <p class="text-gray-600 text-sm">Analyze content readability and accessibility</p>
                        </a>

                        <a href="keyword-density-analyzer.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Keyword Density Analyzer</h3>
                            <p class="text-gray-600 text-sm">Analyze keyword frequency and optimization</p>
                        </a>

                        <a href="serp-checker.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">SERP Checker</h3>
                            <p class="text-gray-600 text-sm">Check search engine ranking positions</p>
                        </a>

                        <a href="../pages/contact.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Contact LoganixSEO</h3>
                            <p class="text-gray-600 text-sm">Get professional WordPress SEO services</p>
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>

    <script>
        function downloadReport() {
            const results = <?php echo json_encode($detection_results); ?>;

            let content = `WordPress Theme Detection Report\n`;
            content += `Generated on: ${new Date().toLocaleDateString()}\n\n`;
            content += `WEBSITE ANALYSIS:\n`;
            content += `URL: ${results.url}\n`;
            content += `WordPress: ${results.is_wordpress ? 'Yes' : 'No'}\n`;

            if (results.wp_version) {
                content += `WordPress Version: ${results.wp_version}\n`;
            }

            content += `\n`;

            if (results.theme) {
                content += `ACTIVE THEME:\n`;
                content += `Name: ${results.theme.name}\n`;
                content += `Slug: ${results.theme.slug}\n`;
                if (results.theme.version) content += `Version: ${results.theme.version}\n`;
                if (results.theme.author) content += `Author: ${results.theme.author}\n`;
                if (results.theme.description) content += `Description: ${results.theme.description}\n`;
                content += `\n`;
            }

            if (results.plugins && results.plugins.length > 0) {
                content += `DETECTED PLUGINS (${results.plugins.length}):\n`;
                results.plugins.forEach((plugin, index) => {
                    content += `${index + 1}. ${plugin.name} (${plugin.slug})\n`;
                });
                content += `\n`;
            }

            if (results.additional_info && Object.keys(results.additional_info).length > 0) {
                content += `ADDITIONAL INFORMATION:\n`;
                Object.entries(results.additional_info).forEach(([key, value]) => {
                    content += `${key.replace('_', ' ').toUpperCase()}: ${value}\n`;
                });
            }

            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'wordpress-theme-detection-report.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function shareResults() {
            const url = "<?php echo htmlspecialchars($input_url); ?>";
            if (navigator.share) {
                navigator.share({
                    title: 'WordPress Theme Detection Results',
                    text: `Check out the WordPress theme and plugins used by ${url}`,
                    url: window.location.href
                });
            } else {
                // Fallback to copying URL
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Results URL copied to clipboard!');
                });
            }
        }

        function toggleFAQ(button) {
            const content = button.nextElementSibling;
            const icon = button.querySelector('svg');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }

        function tryDemo() {
            document.getElementById('url').value = 'https://demo.wordpress.org';
            document.querySelector('form').submit();
        }

        // Add loading states for images
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img[loading="lazy"]');

            images.forEach(img => {
                img.addEventListener('loadstart', function() {
                    this.classList.add('image-loading');
                });

                img.addEventListener('load', function() {
                    this.classList.remove('image-loading');
                });

                img.addEventListener('error', function() {
                    this.classList.remove('image-loading');
                });
            });
        });
    </script>
</body>
</html>
