<?php
// Sitemap Status & Health Check
require_once '../config/config.php';
require_once '../includes/sitemap-updater.php';

// Get sitemap info
$sitemap_url = SITE_URL . '/sitemap.xml.php';
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);

// Test sitemap generation
$sitemap_test = false;
$sitemap_content = '';
$sitemap_error = '';
$url_count = 0;

try {
    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'LoganixSEO Status Checker'
        ]
    ]);
    
    $sitemap_content = @file_get_contents($sitemap_url . '?debug', false, $context);
    
    if ($sitemap_content !== false && (strpos($sitemap_content, '<?xml') !== false || strpos($sitemap_content, '<urlset') !== false)) {
        $sitemap_test = true;
        // Count URLs in sitemap
        $url_count = substr_count($sitemap_content, '<url>');
    } else {
        $sitemap_error = 'Invalid XML response or empty content';
    }
} catch (Exception $e) {
    $sitemap_error = $e->getMessage();
}

// Check file permissions
$files_to_check = [
    'sitemap.xml' => '../sitemap.xml',
    'sitemap.xml.php' => '../sitemap.xml.php',
    'robots.txt' => '../robots.txt',
    'logs directory' => '../logs/',
    'sitemap updater' => '../includes/sitemap-updater.php'
];

$file_status = [];
foreach ($files_to_check as $name => $path) {
    $file_status[$name] = [
        'exists' => file_exists($path),
        'readable' => is_readable($path),
        'writable' => is_writable($path),
        'size' => file_exists($path) ? filesize($path) : 0
    ];
}

// Get recent logs
$updater = new SitemapUpdater();
$recent_logs = $updater->getRecentLogs(10);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sitemap Status - LoganixSEO Admin</title>
    <link rel="stylesheet" href="../assets/fonts/fonts.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Poppins', sans-serif; }
        .status-good { color: #10B981; }
        .status-bad { color: #EF4444; }
        .status-warning { color: #F59E0B; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">📊 Sitemap Status & Health Check</h1>
                <p class="text-gray-600">Monitor your sitemap system health and performance</p>
                <div class="flex space-x-4 mt-4">
                    <a href="sitemap-manager.php" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        ← Sitemap Manager
                    </a>
                    <button onclick="location.reload()" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                        🔄 Refresh Status
                    </button>
                </div>
            </div>

            <!-- Overall Status -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">🎯 Overall Status</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center p-4 rounded-lg <?php echo $sitemap_test ? 'bg-green-100' : 'bg-red-100'; ?>">
                        <div class="text-2xl mb-2"><?php echo $sitemap_test ? '✅' : '❌'; ?></div>
                        <div class="font-semibold <?php echo $sitemap_test ? 'status-good' : 'status-bad'; ?>">
                            Sitemap Generation
                        </div>
                        <div class="text-sm text-gray-600">
                            <?php echo $sitemap_test ? 'Working' : 'Failed'; ?>
                        </div>
                    </div>
                    
                    <div class="text-center p-4 rounded-lg bg-blue-100">
                        <div class="text-2xl mb-2">📊</div>
                        <div class="font-semibold text-blue-600">Total URLs</div>
                        <div class="text-sm text-gray-600"><?php echo $url_count; ?> URLs</div>
                    </div>
                    
                    <div class="text-center p-4 rounded-lg <?php echo $is_localhost ? 'bg-yellow-100' : 'bg-green-100'; ?>">
                        <div class="text-2xl mb-2"><?php echo $is_localhost ? '🏠' : '🌐'; ?></div>
                        <div class="font-semibold <?php echo $is_localhost ? 'status-warning' : 'status-good'; ?>">
                            Environment
                        </div>
                        <div class="text-sm text-gray-600">
                            <?php echo $is_localhost ? 'Localhost' : 'Live Server'; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Status -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">📁 File Status</h2>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-2">File</th>
                                <th class="text-center py-2">Exists</th>
                                <th class="text-center py-2">Readable</th>
                                <th class="text-center py-2">Writable</th>
                                <th class="text-right py-2">Size</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($file_status as $name => $status): ?>
                            <tr class="border-b">
                                <td class="py-2 font-medium"><?php echo htmlspecialchars($name); ?></td>
                                <td class="text-center py-2">
                                    <span class="<?php echo $status['exists'] ? 'status-good' : 'status-bad'; ?>">
                                        <?php echo $status['exists'] ? '✅' : '❌'; ?>
                                    </span>
                                </td>
                                <td class="text-center py-2">
                                    <span class="<?php echo $status['readable'] ? 'status-good' : 'status-bad'; ?>">
                                        <?php echo $status['readable'] ? '✅' : '❌'; ?>
                                    </span>
                                </td>
                                <td class="text-center py-2">
                                    <span class="<?php echo $status['writable'] ? 'status-good' : 'status-warning'; ?>">
                                        <?php echo $status['writable'] ? '✅' : '⚠️'; ?>
                                    </span>
                                </td>
                                <td class="text-right py-2">
                                    <?php echo $status['size'] > 0 ? number_format($status['size']) . ' bytes' : '-'; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Sitemap Details -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">🗺️ Sitemap Details</h2>
                
                <?php if ($sitemap_test): ?>
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h3 class="font-semibold text-gray-700 mb-2">Sitemap URLs</h3>
                                <ul class="space-y-1 text-sm">
                                    <li><a href="../sitemap.xml" target="_blank" class="text-blue-600 hover:underline">Main Sitemap</a></li>
                                    <li><a href="../sitemap.xml.php" target="_blank" class="text-blue-600 hover:underline">Dynamic Generator</a></li>
                                    <li><a href="../sitemap.xml.php?debug" target="_blank" class="text-blue-600 hover:underline">Debug Mode</a></li>
                                </ul>
                            </div>
                            
                            <div>
                                <h3 class="font-semibold text-gray-700 mb-2">Statistics</h3>
                                <ul class="space-y-1 text-sm">
                                    <li><strong>Total URLs:</strong> <?php echo $url_count; ?></li>
                                    <li><strong>Content Size:</strong> <?php echo number_format(strlen($sitemap_content)); ?> bytes</li>
                                    <li><strong>Last Generated:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 class="font-semibold text-red-800 mb-2">❌ Sitemap Generation Failed</h3>
                        <p class="text-red-700 text-sm"><?php echo htmlspecialchars($sitemap_error); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">📋 Recent Activity</h2>
                
                <?php if (empty($recent_logs)): ?>
                    <p class="text-gray-500 italic">No recent activity found.</p>
                <?php else: ?>
                    <div class="space-y-2 max-h-64 overflow-y-auto">
                        <?php foreach (array_reverse($recent_logs) as $log): ?>
                            <div class="p-3 bg-gray-50 rounded border-l-4 <?php 
                                if (strpos($log, 'failed') !== false || strpos($log, 'Failed') !== false) {
                                    echo 'border-red-500';
                                } elseif (strpos($log, 'success') !== false || strpos($log, 'Success') !== false) {
                                    echo 'border-green-500';
                                } else {
                                    echo 'border-blue-500';
                                }
                            ?>">
                                <code class="text-sm text-gray-700"><?php echo htmlspecialchars($log); ?></code>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Environment Info -->
            <?php if ($is_localhost): ?>
            <div class="bg-yellow-50 rounded-lg p-6 mt-6">
                <h2 class="text-xl font-semibold mb-4 text-yellow-800">🏠 Localhost Environment</h2>
                <div class="text-sm text-yellow-700 space-y-2">
                    <p>• Search engine notifications are disabled on localhost</p>
                    <p>• Sitemap generation and file operations work normally</p>
                    <p>• Use the admin dashboard to test functionality</p>
                    <p>• Deploy to live server for full search engine integration</p>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
