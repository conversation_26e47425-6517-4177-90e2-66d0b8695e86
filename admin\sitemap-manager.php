<?php
// Sitemap Management Dashboard
require_once '../config/config.php';
require_once '../includes/sitemap-updater.php';

// Handle form submissions
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'regenerate':
                $updater = new SitemapUpdater();
                $result = $updater->fullUpdate('admin_dashboard');

                if ($result['success']) {
                    $message = isset($result['message']) ? $result['message'] : 'Sitemap updated successfully!';
                    $message_type = 'success';

                    // Add notification details if available
                    if (isset($result['notifications'])) {
                        $notifications = $result['notifications'];
                        $success_count = 0;
                        $details = [];

                        foreach ($notifications as $engine => $status) {
                            if (strpos($status, 'Success') !== false) {
                                $success_count++;
                            }
                            $details[] = "$engine: $status";
                        }

                        $message .= ' | Notifications: ' . implode(', ', $details);
                    }
                } else {
                    $message = isset($result['message']) ? $result['message'] : 'Sitemap regeneration failed. Check logs for details.';
                    $message_type = 'error';
                }
                break;
                
            case 'notify_only':
                $updater = new SitemapUpdater();
                $results = $updater->notifySearchEngines();
                
                $success_count = count(array_filter($results, function($r) { return $r === 'Success'; }));
                $total_count = count($results);
                
                $message = "Search engines notified: $success_count/$total_count successful";
                $message_type = $success_count > 0 ? 'success' : 'error';
                break;
        }
    }
}

// Get recent logs
$updater = new SitemapUpdater();
$recent_logs = $updater->getRecentLogs(20);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sitemap Manager - LoganixSEO Admin</title>
    <link rel="stylesheet" href="../assets/fonts/fonts.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Poppins', sans-serif; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🗺️ Sitemap Manager</h1>
                <p class="text-gray-600">Manage your website sitemap and search engine notifications</p>
                <a href="../index.php" class="inline-block mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    ← Back to Website
                </a>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
            <div class="p-4 rounded-lg mb-6 <?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
            <?php endif; ?>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <form method="POST" class="inline">
                        <input type="hidden" name="action" value="regenerate">
                        <button type="submit" class="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            🔄 Regenerate Sitemap & Notify Search Engines
                        </button>
                    </form>
                    
                    <form method="POST" class="inline">
                        <input type="hidden" name="action" value="notify_only">
                        <button type="submit" class="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            📢 Notify Search Engines Only
                        </button>
                    </form>
                </div>
                
                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="../sitemap.xml" target="_blank" class="block text-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        👁️ View Current Sitemap
                    </a>
                    
                    <a href="search-console-guide.php" class="block text-center px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        🔍 Search Console Setup Guide
                    </a>
                </div>

                <div class="mt-4">
                    <a href="sitemap-status.php" class="block text-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                        📊 View Detailed Status & Health Check
                    </a>
                </div>
            </div>

            <!-- Sitemap Info -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Sitemap Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2">Sitemap URLs</h3>
                        <ul class="space-y-1 text-sm">
                            <li><strong>Main Sitemap:</strong> <a href="../sitemap.xml" class="text-blue-600 hover:underline" target="_blank"><?php echo SITE_URL; ?>/sitemap.xml</a></li>
                            <li><strong>Dynamic Generator:</strong> <a href="../sitemap.xml.php" class="text-blue-600 hover:underline" target="_blank"><?php echo SITE_URL; ?>/sitemap.xml.php</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2">Auto-Update Triggers</h3>
                        <ul class="space-y-1 text-sm text-gray-600">
                            <li>✅ New tool creation</li>
                            <li>✅ New page creation</li>
                            <li>✅ New blog post publish</li>
                            <li>✅ Manual updates</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Recent Logs -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Recent Activity</h2>

                <?php if (empty($recent_logs)): ?>
                    <p class="text-gray-500 italic">No recent activity found.</p>
                <?php else: ?>
                    <div class="space-y-2 max-h-96 overflow-y-auto">
                        <?php foreach (array_reverse($recent_logs) as $log): ?>
                            <?php
                            $border_color = 'border-blue-500';
                            $bg_color = 'bg-gray-50';

                            if (strpos($log, 'failed') !== false || strpos($log, 'Failed') !== false || strpos($log, 'error') !== false) {
                                $border_color = 'border-red-500';
                                $bg_color = 'bg-red-50';
                            } elseif (strpos($log, 'success') !== false || strpos($log, 'Success') !== false || strpos($log, 'completed') !== false) {
                                $border_color = 'border-green-500';
                                $bg_color = 'bg-green-50';
                            } elseif (strpos($log, 'skipped') !== false || strpos($log, 'Skipped') !== false) {
                                $border_color = 'border-yellow-500';
                                $bg_color = 'bg-yellow-50';
                            }
                            ?>
                            <div class="p-3 <?php echo $bg_color; ?> rounded border-l-4 <?php echo $border_color; ?>">
                                <code class="text-sm text-gray-700"><?php echo htmlspecialchars($log); ?></code>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Usage Instructions -->
            <div class="bg-blue-50 rounded-lg p-6 mt-6">
                <h2 class="text-xl font-semibold mb-4 text-blue-800">🔧 How to Use Auto-Update</h2>
                
                <div class="space-y-4 text-sm">
                    <div>
                        <h3 class="font-semibold text-blue-700">For New Tools:</h3>
                        <code class="bg-white p-2 rounded block mt-1">
                            require_once 'includes/sitemap-updater.php';<br>
                            sitemapUpdateForNewTool('tool-name.php');
                        </code>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold text-blue-700">For New Pages:</h3>
                        <code class="bg-white p-2 rounded block mt-1">
                            require_once 'includes/sitemap-updater.php';<br>
                            sitemapUpdateForNewPage('page-name.php');
                        </code>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold text-blue-700">For New Posts:</h3>
                        <code class="bg-white p-2 rounded block mt-1">
                            require_once 'includes/sitemap-updater.php';<br>
                            sitemapUpdateForNewPost('Post Title');
                        </code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
