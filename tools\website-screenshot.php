<?php
session_start();
require_once '../config/config.php';

$page_title = 'Free Website Screenshot Tool - Capture Full Page Screenshots | LoganixSEO';
$page_description = 'Capture full-page website screenshots for free. Our website screenshot tool helps you take high-quality screenshots of any webpage for analysis, documentation, and SEO audits.';
$page_keywords = 'website screenshot, webpage capture, full page screenshot, website preview, page screenshot tool, web capture, screenshot generator, website image';

// Get current page for navigation
$current_page = 'tools';

$screenshot_result = [];
$form_data = [];

if ($_POST && !empty($_POST['url'])) {
    $form_data = [
        'url' => trim($_POST['url']),
        'device' => $_POST['device'] ?? 'desktop',
        'full_page' => !empty($_POST['full_page']),
        'delay' => intval($_POST['delay'] ?? 2)
    ];
    
    // Validate URL
    if (filter_var($form_data['url'], FILTER_VALIDATE_URL)) {
        // Simulate screenshot generation (In real implementation, you would use a service like Puppeteer, Selenium, or API)
        $screenshot_result = [
            'success' => true,
            'url' => $form_data['url'],
            'device' => $form_data['device'],
            'full_page' => $form_data['full_page'],
            'timestamp' => date('Y-m-d H:i:s'),
            'file_size' => rand(150, 800) . ' KB',
            'dimensions' => $form_data['device'] === 'mobile' ? '375x812' : ($form_data['device'] === 'tablet' ? '768x1024' : '1920x1080'),
            'screenshot_url' => 'https://via.placeholder.com/' . ($form_data['device'] === 'mobile' ? '375x812' : ($form_data['device'] === 'tablet' ? '768x1024' : '1920x1080')) . '/4A90E2/FFFFFF?text=Screenshot+Preview',
            'download_url' => '#'
        ];
    } else {
        $screenshot_result = [
            'success' => false,
            'error' => 'Please enter a valid URL (including http:// or https://)'
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/website-screenshot-tool.jpg">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">
    <meta name="twitter:image" content="<?php echo SITE_URL; ?>/assets/images/website-screenshot-tool.jpg">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Website Screenshot Tool",
        "description": "<?php echo $page_description; ?>",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "SEO Tool",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "LoganixSEO",
            "url": "<?php echo SITE_URL; ?>"
        },
        "featureList": [
            "Capture full-page screenshots",
            "Multiple device views",
            "High-quality images",
            "Instant preview",
            "Free to use"
        ]
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-16 lg:py-20 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <div class="mb-6">
                        <span class="text-4xl lg:text-5xl">📸</span>
                    </div>
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-6 lg:mb-8" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Free Website Screenshot Tool
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-8 lg:mb-10" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Capture full-page website screenshots for analysis, documentation, and SEO audits. High-quality images in multiple device formats.
                    </p>
                    
                    <!-- Breadcrumb -->
                    <nav class="text-white/80 text-sm mb-8">
                        <a href="../index.php" class="hover:text-white">Home</a>
                        <span class="mx-2">›</span>
                        <a href="../pages/tools.php" class="hover:text-white">SEO Tools</a>
                        <span class="mx-2">›</span>
                        <span class="text-white">Website Screenshot</span>
                    </nav>
                </div>
            </div>
        </section>

        <!-- Tool Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 50%, #E2E8F0 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
                <div class="rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <form method="POST" class="space-y-6">
                        <div>
                            <label for="url" class="block text-sm font-bold text-gray-900 mb-2">Website URL *</label>
                            <input type="url" id="url" name="url" required 
                                   value="<?php echo htmlspecialchars($form_data['url'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors duration-300" 
                                   placeholder="https://example.com">
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="device" class="block text-sm font-bold text-gray-900 mb-2">Device View</label>
                                <select id="device" name="device" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors duration-300">
                                    <option value="desktop" <?php echo ($form_data['device'] ?? 'desktop') === 'desktop' ? 'selected' : ''; ?>>Desktop (1920x1080)</option>
                                    <option value="tablet" <?php echo ($form_data['device'] ?? '') === 'tablet' ? 'selected' : ''; ?>>Tablet (768x1024)</option>
                                    <option value="mobile" <?php echo ($form_data['device'] ?? '') === 'mobile' ? 'selected' : ''; ?>>Mobile (375x812)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="delay" class="block text-sm font-bold text-gray-900 mb-2">Load Delay (seconds)</label>
                                <input type="number" id="delay" name="delay" min="0" max="10" 
                                       value="<?php echo $form_data['delay'] ?? 2; ?>"
                                       class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors duration-300">
                            </div>
                        </div>
                        
                        <div>
                            <label class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <input type="checkbox" name="full_page" value="1" 
                                       <?php echo empty($_POST) || !empty($_POST['full_page']) ? 'checked' : ''; ?>
                                       class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <span class="text-gray-700 font-medium">Capture Full Page (including content below the fold)</span>
                            </label>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="px-8 py-4 text-lg font-bold text-white rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 inline-flex items-center space-x-2" style="background: linear-gradient(45deg, #3B82F6, #1E40AF);">
                                <span>Capture Screenshot</span>
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Results Section -->
                <?php if (!empty($screenshot_result)): ?>
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <?php if ($screenshot_result['success']): ?>
                        <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                            Screenshot Captured Successfully
                        </h2>
                        
                        <!-- Screenshot Info -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-lg font-bold text-blue-600"><?php echo ucfirst($screenshot_result['device']); ?></div>
                                <div class="text-sm text-blue-800">Device View</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-lg font-bold text-green-600"><?php echo $screenshot_result['dimensions']; ?></div>
                                <div class="text-sm text-green-800">Dimensions</div>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div class="text-lg font-bold text-purple-600"><?php echo $screenshot_result['file_size']; ?></div>
                                <div class="text-sm text-purple-800">File Size</div>
                            </div>
                            <div class="text-center p-4 bg-orange-50 rounded-lg">
                                <div class="text-lg font-bold text-orange-600"><?php echo $screenshot_result['full_page'] ? 'Full' : 'Viewport'; ?></div>
                                <div class="text-sm text-orange-800">Page Type</div>
                            </div>
                        </div>
                        
                        <!-- Screenshot Preview -->
                        <div class="text-center mb-6">
                            <img src="<?php echo $screenshot_result['screenshot_url']; ?>" 
                                 alt="Website Screenshot Preview" 
                                 class="max-w-full h-auto rounded-lg shadow-lg mx-auto"
                                 style="max-height: 400px;">
                        </div>
                        
                        <div class="text-center">
                            <button onclick="downloadScreenshot()" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-300 mr-4">
                                Download Screenshot
                            </button>
                            <button onclick="shareScreenshot()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                                Share Screenshot
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="text-center p-6 bg-red-50 border border-red-200 rounded-lg">
                            <div class="text-red-600 font-semibold mb-2">Screenshot Failed</div>
                            <div class="text-red-500"><?php echo htmlspecialchars($screenshot_result['error']); ?></div>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Tool Description -->
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        About Our Website Screenshot Tool
                    </h2>

                    <div class="prose max-w-none text-gray-700 leading-relaxed">
                        <p class="mb-4">
                            Our free Website Screenshot Tool is a powerful utility that captures high-quality, full-page screenshots of any website across multiple device formats. This essential tool helps web developers, designers, SEO professionals, and digital marketers document website appearances, conduct visual audits, track design changes, and create professional presentations with accurate website representations.
                        </p>

                        <p class="mb-4">
                            The tool integrates seamlessly with your <a href="../services/technical-seo.php" class="text-blue-600 hover:text-blue-800 font-semibold">technical SEO workflow</a>, enabling you to capture before-and-after screenshots for website optimization projects, document mobile responsiveness issues, and create visual reports for clients. Our screenshot tool supports desktop, tablet, and mobile viewports, ensuring comprehensive cross-device documentation.
                        </p>

                        <p class="mb-4">
                            Perfect for <a href="../services/seo-services.php" class="text-blue-600 hover:text-blue-800 font-semibold">SEO audits and reporting</a>, our tool captures full-page screenshots including content below the fold, providing complete visual documentation of website layouts, design elements, and user interface components. The tool offers customizable load delays to ensure dynamic content is fully rendered before capture.
                        </p>

                        <p>
                            Combined with our <a href="../services/content-writing.php" class="text-blue-600 hover:text-blue-800 font-semibold">web design and development services</a>, website screenshots serve as valuable documentation for design reviews, client presentations, competitive analysis, and quality assurance testing. Use this tool to maintain visual records of website changes and ensure consistent user experiences across all devices.
                        </p>
                    </div>
                </div>

                <!-- FAQ Section -->
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        Frequently Asked Questions
                    </h2>

                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                What's the difference between viewport and full-page screenshots?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Viewport screenshots capture only what's visible in the browser window, while full-page screenshots capture the entire webpage including content that requires scrolling to view. Full-page is better for comprehensive documentation.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                Why should I use different device views?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Different device views help you see how your website appears on various screen sizes. This is crucial for responsive design testing, mobile optimization, and ensuring consistent user experience across all devices.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                What is the load delay setting for?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Load delay allows time for dynamic content, JavaScript, and images to fully load before capturing the screenshot. Increase delay for websites with heavy JavaScript or slow-loading elements.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                Can I screenshot password-protected or private pages?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Our tool can only capture publicly accessible pages. For password-protected or private pages, you'll need to use browser extensions or desktop screenshot tools while logged in to those sites.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Tools -->
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        Related SEO Tools
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <a href="website-analyzer.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Website Analyzer</h3>
                            <p class="text-gray-600 text-sm">Comprehensive website SEO analysis</p>
                        </a>

                        <a href="website-speed-test.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Website Speed Test</h3>
                            <p class="text-gray-600 text-sm">Test website loading speed and performance</p>
                        </a>

                        <a href="mobile-friendly-test.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Mobile Friendly Test</h3>
                            <p class="text-gray-600 text-sm">Check mobile responsiveness</p>
                        </a>

                        <a href="seo-calculator.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">SEO Calculator</h3>
                            <p class="text-gray-600 text-sm">Calculate SEO performance scores</p>
                        </a>

                        <a href="broken-link-checker.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Broken Link Checker</h3>
                            <p class="text-gray-600 text-sm">Find and fix broken links</p>
                        </a>

                        <a href="../pages/contact.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Contact LoganixSEO</h3>
                            <p class="text-gray-600 text-sm">Get professional web design services</p>
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        function downloadScreenshot() {
            // In real implementation, this would download the actual screenshot
            alert('Screenshot download would start here. This is a demo version.');
        }
        
        function shareScreenshot() {
            const url = "<?php echo htmlspecialchars($form_data['url'] ?? ''); ?>";
            if (navigator.share) {
                navigator.share({
                    title: 'Website Screenshot',
                    text: `Screenshot of ${url}`,
                    url: window.location.href
                });
            } else {
                // Fallback to copying URL
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Screenshot URL copied to clipboard!');
                });
            }
        }

        function toggleFAQ(button) {
            const content = button.nextElementSibling;
            const icon = button.querySelector('svg');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }
    </script>
</body>
</html>
