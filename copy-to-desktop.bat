@echo off
echo Copying LoganixSEO project to Desktop...

REM Get the current directory (should be the project folder)
set SOURCE=%CD%

REM Set destination to Desktop
set DEST=%USERPROFILE%\Desktop\loganixseo.com

echo Source: %SOURCE%
echo Destination: %DEST%

REM Create destination directory if it doesn't exist
if not exist "%DEST%" mkdir "%DEST%"

REM Copy all files and folders
xcopy "%SOURCE%\*" "%DEST%\" /E /I /H /Y /Q

echo.
echo ✅ Project copied successfully to Desktop!
echo Location: %DEST%
echo.
echo You can now:
echo 1. Upload this folder to your live hosting
echo 2. Run fix-wordpress-urls.php after upload
echo 3. Update WordPress permalinks
echo.
pause
