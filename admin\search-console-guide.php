<?php
require_once '../config/config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Search Console Setup Guide - LoganixSEO</title>
    <link rel="stylesheet" href="../assets/fonts/fonts.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Poppins', sans-serif; }
        .step-box { background: linear-gradient(135deg, #EFF6FF, #DBEAFE); }
        .url-box { background: #f8f9fa; border: 2px solid #3B82F6; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🔍 Google Search Console Setup</h1>
                <p class="text-gray-600">Complete guide to submit your sitemap and monitor your website's performance</p>
                <div class="flex space-x-4 mt-4">
                    <a href="sitemap-manager.php" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        ← Sitemap Manager
                    </a>
                    <a href="../index.php" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                        ← Back to Website
                    </a>
                </div>
            </div>

            <!-- Important URLs -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">📋 Important URLs for Search Console</h2>
                
                <div class="space-y-4">
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2">🗺️ Main Sitemap URL (Submit This One):</h3>
                        <div class="url-box p-4 rounded-lg">
                            <code class="text-lg font-mono text-blue-600"><?php echo SITE_URL; ?>/sitemap.xml</code>
                            <div class="mt-2 flex space-x-2">
                                <a href="<?php echo SITE_URL; ?>/sitemap.xml" target="_blank" class="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                    View Sitemap
                                </a>
                                <button onclick="copyToClipboard('<?php echo SITE_URL; ?>/sitemap.xml')" class="text-sm bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700">
                                    Copy URL
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2">🤖 Robots.txt URL:</h3>
                        <div class="url-box p-4 rounded-lg">
                            <code class="text-lg font-mono text-blue-600"><?php echo SITE_URL; ?>/robots.txt</code>
                            <div class="mt-2">
                                <a href="<?php echo SITE_URL; ?>/robots.txt" target="_blank" class="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                    View Robots.txt
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step by Step Guide -->
            <div class="space-y-6">
                <!-- Step 1 -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="step-box p-4 rounded-lg mb-4">
                        <h2 class="text-xl font-bold text-blue-800 mb-2">Step 1: Access Google Search Console</h2>
                    </div>
                    
                    <div class="space-y-3">
                        <p>1. Go to <a href="https://search.google.com/search-console" target="_blank" class="text-blue-600 hover:underline font-semibold">Google Search Console</a></p>
                        <p>2. Sign in with your Google account</p>
                        <p>3. Click <strong>"Add Property"</strong> if this is your first time</p>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="step-box p-4 rounded-lg mb-4">
                        <h2 class="text-xl font-bold text-blue-800 mb-2">Step 2: Add Your Website Property</h2>
                    </div>
                    
                    <div class="space-y-3">
                        <p>1. Choose <strong>"URL prefix"</strong> property type</p>
                        <p>2. Enter your website URL:</p>
                        <div class="url-box p-3 rounded">
                            <code class="font-mono text-blue-600"><?php echo SITE_URL; ?></code>
                        </div>
                        <p>3. Click <strong>"Continue"</strong></p>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="step-box p-4 rounded-lg mb-4">
                        <h2 class="text-xl font-bold text-blue-800 mb-2">Step 3: Verify Ownership</h2>
                    </div>
                    
                    <div class="space-y-3">
                        <p><strong>Recommended Method: HTML file upload</strong></p>
                        <ol class="list-decimal list-inside space-y-2 ml-4">
                            <li>Download the HTML verification file from Google</li>
                            <li>Upload it to your website's root directory</li>
                            <li>Make sure it's accessible at: <code><?php echo SITE_URL; ?>/google[verification-code].html</code></li>
                            <li>Click <strong>"Verify"</strong> in Search Console</li>
                        </ol>
                        
                        <div class="warning p-3 rounded mt-4">
                            <strong>⚠️ Alternative Methods:</strong>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li><strong>HTML tag:</strong> Add meta tag to your website's &lt;head&gt; section</li>
                                <li><strong>Google Analytics:</strong> If you have GA installed</li>
                                <li><strong>Google Tag Manager:</strong> If you use GTM</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="step-box p-4 rounded-lg mb-4">
                        <h2 class="text-xl font-bold text-blue-800 mb-2">Step 4: Submit Your Sitemap</h2>
                    </div>
                    
                    <div class="space-y-3">
                        <p>1. After verification, go to <strong>"Sitemaps"</strong> in the left menu</p>
                        <p>2. Click <strong>"Add a new sitemap"</strong></p>
                        <p>3. Enter your sitemap URL:</p>
                        <div class="url-box p-3 rounded">
                            <code class="font-mono text-blue-600">sitemap.xml</code>
                            <p class="text-sm text-gray-600 mt-1">
                                (Just enter "sitemap.xml" - Google will automatically add your domain)
                            </p>
                        </div>
                        <p>4. Click <strong>"Submit"</strong></p>
                        
                        <div class="success p-3 rounded mt-4">
                            <strong>✅ Success!</strong> Your sitemap will be processed within a few hours to days.
                        </div>
                    </div>
                </div>

                <!-- Step 5 -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="step-box p-4 rounded-lg mb-4">
                        <h2 class="text-xl font-bold text-blue-800 mb-2">Step 5: Monitor Your Performance</h2>
                    </div>
                    
                    <div class="space-y-3">
                        <p><strong>Key sections to monitor:</strong></p>
                        <ul class="list-disc list-inside space-y-2 ml-4">
                            <li><strong>Performance:</strong> See your search rankings and clicks</li>
                            <li><strong>Coverage:</strong> Check for indexing issues</li>
                            <li><strong>Sitemaps:</strong> Monitor sitemap processing status</li>
                            <li><strong>URL Inspection:</strong> Test individual pages</li>
                            <li><strong>Mobile Usability:</strong> Check mobile-friendliness</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Automatic Updates Info -->
            <div class="bg-blue-50 rounded-lg p-6 mt-6">
                <h2 class="text-xl font-semibold mb-4 text-blue-800">🔄 Automatic Sitemap Updates</h2>
                
                <div class="space-y-4">
                    <div class="info p-4 rounded">
                        <h3 class="font-semibold mb-2">✅ Your sitemap updates automatically when:</h3>
                        <ul class="list-disc list-inside space-y-1">
                            <li>New blog posts are published</li>
                            <li>New tools are created</li>
                            <li>New pages are added</li>
                            <li>Content is modified</li>
                        </ul>
                    </div>
                    
                    <div class="warning p-4 rounded">
                        <h3 class="font-semibold mb-2">🔔 Search engines are notified automatically:</h3>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Google is pinged when sitemap updates</li>
                            <li>Bing is also notified</li>
                            <li>No manual resubmission needed</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- WordPress Webhook Setup -->
            <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <h2 class="text-xl font-semibold mb-4">🔗 WordPress Webhook Setup (Optional)</h2>
                
                <p class="mb-4">For instant sitemap updates when you publish posts, add this webhook to your WordPress:</p>
                
                <div class="url-box p-4 rounded-lg mb-4">
                    <code class="text-sm font-mono text-blue-600"><?php echo SITE_URL; ?>/webhook-sitemap-update.php</code>
                    <div class="mt-2">
                        <button onclick="copyToClipboard('<?php echo SITE_URL; ?>/webhook-sitemap-update.php')" class="text-sm bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700">
                            Copy Webhook URL
                        </button>
                        <a href="<?php echo SITE_URL; ?>/webhook-sitemap-update.php?test" target="_blank" class="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 ml-2">
                            Test Webhook
                        </a>
                    </div>
                </div>
                
                <p class="text-sm text-gray-600">
                    Add this URL to your WordPress webhooks or use a plugin like "WP Webhooks" to trigger sitemap updates instantly when content is published.
                </p>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('URL copied to clipboard!');
            }, function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                const textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('URL copied to clipboard!');
                } catch (err) {
                    alert('Failed to copy URL. Please copy manually: ' + text);
                }
                document.body.removeChild(textArea);
            });
        }
    </script>
</body>
</html>
