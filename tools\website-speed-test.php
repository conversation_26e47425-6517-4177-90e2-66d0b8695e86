<?php
require_once '../config/config.php';

$page_title = 'Website Speed Test - Free Page Speed Analyzer & Performance Tool';
$page_description = 'Test your website loading speed and performance. Get detailed analysis of page speed, Core Web Vitals, and optimization recommendations for better user experience.';
$page_keywords = 'website speed test, page speed test, site speed analyzer, performance test, core web vitals, loading speed, website optimization';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Website Speed Test",
        "description": "Test your website loading speed and performance. Get detailed analysis of page speed, Core Web Vitals, and optimization recommendations for better user experience.",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "Performance Tool",
        "operatingSystem": "Any",
        "permissions": "browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "SEO Agency",
            "url": "http://localhost/loganixseo.com"
        }
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main class="pt-20 lg:pt-24">
        <!-- Hero Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-8 lg:mb-10" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Website Speed Test
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-10 lg:mb-12" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Analyze your website's loading speed and get actionable optimization recommendations.
                    </p>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6"></div>

        <!-- Tool Section -->
        <section class="py-8 lg:py-12 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl">
                <div class="rounded-xl shadow-lg p-6 lg:p-8 border-2" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC, #F1F5F9); border-color: #10B981;">
                    <div class="text-center mb-6 lg:mb-8">
                        <h2 class="text-2xl lg:text-3xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #10B981, #059669); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Professional Website Speed Analyzer</h2>
                        <p class="text-gray-700 text-base lg:text-lg font-medium">Test page speed, Core Web Vitals, and get optimization insights</p>
                    </div>

                    <!-- Speed Test Interface -->
                    <div class="max-w-3xl mx-auto">
                        <!-- URL Input -->
                        <div class="mb-6">
                            <label class="block text-gray-700 font-bold mb-3 text-base lg:text-lg">Enter Website URL to Test:</label>
                            <div class="flex flex-col sm:flex-row gap-3">
                                <input type="url" id="websiteUrl" class="flex-1 px-4 py-3 border-2 rounded-lg focus:outline-none text-gray-800 text-base" style="border-color: #A7F3D0; background: linear-gradient(45deg, #ECFDF5, #D1FAE5);" placeholder="https://example.com">
                                <button onclick="startSpeedTest()" id="testBtn" class="px-6 py-3 text-white font-bold rounded-lg transform hover:scale-105 transition-all duration-300 shadow-lg text-base" style="background: linear-gradient(45deg, #10B981, #059669); box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);">
                                    <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    Test Speed
                                </button>
                            </div>
                        </div>

                        <!-- Test Options -->
                        <div class="mb-6">
                            <label class="block text-gray-700 font-bold mb-3 text-base lg:text-lg">Test Configuration:</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-gray-600 font-medium mb-2 text-sm">Device Type:</label>
                                    <select id="deviceType" class="w-full px-3 py-2 border-2 rounded-lg text-sm" style="border-color: #A7F3D0;">
                                        <option value="desktop">Desktop</option>
                                        <option value="mobile">Mobile</option>
                                        <option value="both">Both</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-gray-600 font-medium mb-2 text-sm">Connection:</label>
                                    <select id="connectionType" class="w-full px-3 py-2 border-2 rounded-lg text-sm" style="border-color: #A7F3D0;">
                                        <option value="4g">4G</option>
                                        <option value="3g">3G</option>
                                        <option value="cable">Cable</option>
                                        <option value="fiber">Fiber</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-gray-600 font-medium mb-2 text-sm">Location:</label>
                                    <select id="testLocation" class="w-full px-3 py-2 border-2 rounded-lg text-sm" style="border-color: #A7F3D0;">
                                        <option value="us">United States</option>
                                        <option value="eu">Europe</option>
                                        <option value="asia">Asia</option>
                                        <option value="global">Global Average</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Loading State -->
                        <div id="loadingState" class="hidden text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mb-4"></div>
                            <div class="text-lg font-semibold text-gray-700 mb-2">Testing Website Speed...</div>
                            <div id="loadingProgress" class="text-sm text-gray-500">Initializing test...</div>
                            <div class="w-full bg-gray-200 rounded-full h-2 mt-4 max-w-md mx-auto">
                                <div id="progressBar" class="bg-green-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>

                        <!-- Results Section -->
                        <div id="resultsSection" class="hidden">
                            <!-- Overall Score -->
                            <div class="mb-6 text-center">
                                <div class="inline-block p-6 bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 rounded-xl">
                                    <div class="text-3xl font-bold mb-2" id="overallScore" style="color: #059669;">95</div>
                                    <div class="text-sm text-gray-600 font-medium">Overall Performance Score</div>
                                    <div id="scoreGrade" class="text-lg font-bold text-green-600 mt-1">Excellent</div>
                                </div>
                            </div>

                            <!-- Core Web Vitals -->
                            <div class="mb-6">
                                <h3 class="text-lg font-bold text-gray-800 mb-4">Core Web Vitals</h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="p-4 bg-white rounded-lg border shadow-sm">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-medium text-gray-600">LCP</span>
                                            <span id="lcpStatus" class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Good</span>
                                        </div>
                                        <div id="lcpValue" class="text-2xl font-bold text-gray-900">1.2s</div>
                                        <div class="text-xs text-gray-500">Largest Contentful Paint</div>
                                    </div>
                                    <div class="p-4 bg-white rounded-lg border shadow-sm">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-medium text-gray-600">FID</span>
                                            <span id="fidStatus" class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Good</span>
                                        </div>
                                        <div id="fidValue" class="text-2xl font-bold text-gray-900">45ms</div>
                                        <div class="text-xs text-gray-500">First Input Delay</div>
                                    </div>
                                    <div class="p-4 bg-white rounded-lg border shadow-sm">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-medium text-gray-600">CLS</span>
                                            <span id="clsStatus" class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Good</span>
                                        </div>
                                        <div id="clsValue" class="text-2xl font-bold text-gray-900">0.05</div>
                                        <div class="text-xs text-gray-500">Cumulative Layout Shift</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Performance Metrics -->
                            <div class="mb-6">
                                <h3 class="text-lg font-bold text-gray-800 mb-4">Performance Metrics</h3>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div class="text-center p-3 bg-white rounded-lg border">
                                        <div id="loadTime" class="text-xl font-bold text-blue-600">2.1s</div>
                                        <div class="text-xs text-gray-600">Load Time</div>
                                    </div>
                                    <div class="text-center p-3 bg-white rounded-lg border">
                                        <div id="pageSize" class="text-xl font-bold text-purple-600">1.2MB</div>
                                        <div class="text-xs text-gray-600">Page Size</div>
                                    </div>
                                    <div class="text-center p-3 bg-white rounded-lg border">
                                        <div id="requests" class="text-xl font-bold text-orange-600">45</div>
                                        <div class="text-xs text-gray-600">Requests</div>
                                    </div>
                                    <div class="text-center p-3 bg-white rounded-lg border">
                                        <div id="ttfb" class="text-xl font-bold text-green-600">180ms</div>
                                        <div class="text-xs text-gray-600">TTFB</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Optimization Opportunities -->
                            <div class="mb-6">
                                <h3 class="text-lg font-bold text-gray-800 mb-4">Optimization Opportunities</h3>
                                <div id="optimizationList" class="space-y-3">
                                    <!-- Dynamic content will be added here -->
                                </div>
                            </div>

                            <!-- Resource Breakdown -->
                            <div class="mb-6">
                                <h3 class="text-lg font-bold text-gray-800 mb-4">Resource Breakdown</h3>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div class="text-center p-3 bg-white rounded-lg border">
                                        <div id="htmlSize" class="text-lg font-bold text-blue-600">45KB</div>
                                        <div class="text-xs text-gray-600">HTML</div>
                                    </div>
                                    <div class="text-center p-3 bg-white rounded-lg border">
                                        <div id="cssSize" class="text-lg font-bold text-green-600">120KB</div>
                                        <div class="text-xs text-gray-600">CSS</div>
                                    </div>
                                    <div class="text-center p-3 bg-white rounded-lg border">
                                        <div id="jsSize" class="text-lg font-bold text-yellow-600">350KB</div>
                                        <div class="text-xs text-gray-600">JavaScript</div>
                                    </div>
                                    <div class="text-center p-3 bg-white rounded-lg border">
                                        <div id="imageSize" class="text-lg font-bold text-purple-600">680KB</div>
                                        <div class="text-xs text-gray-600">Images</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="text-center">
                            <div class="flex flex-wrap justify-center gap-3">
                                <button onclick="clearResults()" class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-300 text-sm">
                                    Clear Results
                                </button>
                                <button onclick="exportReport()" class="px-4 py-2 text-green-600 hover:text-green-800 border border-green-300 rounded-lg hover:bg-green-50 transition-all duration-300 text-sm">
                                    Export Report
                                </button>
                                <button onclick="compareWebsites()" class="px-4 py-2 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-lg hover:bg-blue-50 transition-all duration-300 text-sm">
                                    Compare Sites
                                </button>
                                <button onclick="scheduleTest()" class="px-4 py-2 text-purple-600 hover:text-purple-800 border border-purple-300 rounded-lg hover:bg-purple-50 transition-all duration-300 text-sm">
                                    Schedule Tests
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>

    <script>
        let testInProgress = false;

        function startSpeedTest() {
            const websiteUrl = document.getElementById('websiteUrl').value.trim();
            
            if (!websiteUrl) {
                alert('Please enter a website URL to test.');
                return;
            }

            // Validate URL
            try {
                new URL(websiteUrl);
            } catch (e) {
                alert('Please enter a valid URL (including http:// or https://).');
                return;
            }

            if (testInProgress) {
                alert('A test is already in progress. Please wait for it to complete.');
                return;
            }

            testInProgress = true;
            showLoadingState();
            simulateSpeedTest();
        }

        function showLoadingState() {
            document.getElementById('loadingState').classList.remove('hidden');
            document.getElementById('resultsSection').classList.add('hidden');
            document.getElementById('testBtn').disabled = true;
            document.getElementById('testBtn').textContent = 'Testing...';
        }

        function simulateSpeedTest() {
            const steps = [
                'Connecting to server...',
                'Analyzing page structure...',
                'Measuring load times...',
                'Testing Core Web Vitals...',
                'Analyzing resources...',
                'Generating recommendations...',
                'Finalizing report...'
            ];

            let currentStep = 0;
            const progressInterval = setInterval(() => {
                if (currentStep < steps.length) {
                    document.getElementById('loadingProgress').textContent = steps[currentStep];
                    document.getElementById('progressBar').style.width = `${((currentStep + 1) / steps.length) * 100}%`;
                    currentStep++;
                } else {
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        showResults();
                    }, 1000);
                }
            }, 800);
        }

        function showResults() {
            // Generate realistic test results
            const deviceType = document.getElementById('deviceType').value;
            const connectionType = document.getElementById('connectionType').value;
            
            // Simulate different results based on device and connection
            const baseScore = deviceType === 'mobile' ? 75 : 85;
            const connectionMultiplier = {
                '3g': 0.7,
                '4g': 0.9,
                'cable': 1.0,
                'fiber': 1.1
            };
            
            const finalScore = Math.min(100, Math.round(baseScore * connectionMultiplier[connectionType] + Math.random() * 10));
            
            // Update overall score
            document.getElementById('overallScore').textContent = finalScore;
            
            // Update grade
            let grade, gradeColor;
            if (finalScore >= 90) {
                grade = 'Excellent';
                gradeColor = '#059669';
            } else if (finalScore >= 75) {
                grade = 'Good';
                gradeColor = '#D97706';
            } else if (finalScore >= 60) {
                grade = 'Needs Improvement';
                gradeColor = '#DC2626';
            } else {
                grade = 'Poor';
                gradeColor = '#DC2626';
            }
            
            document.getElementById('scoreGrade').textContent = grade;
            document.getElementById('scoreGrade').style.color = gradeColor;

            // Update Core Web Vitals
            updateCoreWebVitals(deviceType, connectionType);
            
            // Update performance metrics
            updatePerformanceMetrics(deviceType, connectionType);
            
            // Generate optimization opportunities
            generateOptimizationOpportunities(finalScore);
            
            // Update resource breakdown
            updateResourceBreakdown();

            // Show results and hide loading
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('resultsSection').classList.remove('hidden');
            document.getElementById('testBtn').disabled = false;
            document.getElementById('testBtn').innerHTML = `
                <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Test Speed
            `;
            
            testInProgress = false;

            // Scroll to results
            document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
        }

        function updateCoreWebVitals(deviceType, connectionType) {
            // Simulate realistic Core Web Vitals based on device and connection
            const isMobile = deviceType === 'mobile';
            const connectionSpeed = {
                '3g': 0.5,
                '4g': 0.8,
                'cable': 1.0,
                'fiber': 1.2
            }[connectionType];

            // LCP (Largest Contentful Paint)
            const baseLCP = isMobile ? 2.8 : 2.2;
            const lcp = (baseLCP / connectionSpeed + Math.random() * 0.5).toFixed(1);
            document.getElementById('lcpValue').textContent = lcp + 's';
            
            const lcpStatus = lcp <= 2.5 ? 'Good' : lcp <= 4.0 ? 'Needs Improvement' : 'Poor';
            const lcpColor = lcp <= 2.5 ? 'bg-green-100 text-green-800' : lcp <= 4.0 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800';
            document.getElementById('lcpStatus').textContent = lcpStatus;
            document.getElementById('lcpStatus').className = `px-2 py-1 text-xs rounded-full ${lcpColor}`;

            // FID (First Input Delay)
            const baseFID = isMobile ? 80 : 60;
            const fid = Math.round(baseFID / connectionSpeed + Math.random() * 20);
            document.getElementById('fidValue').textContent = fid + 'ms';
            
            const fidStatus = fid <= 100 ? 'Good' : fid <= 300 ? 'Needs Improvement' : 'Poor';
            const fidColor = fid <= 100 ? 'bg-green-100 text-green-800' : fid <= 300 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800';
            document.getElementById('fidStatus').textContent = fidStatus;
            document.getElementById('fidStatus').className = `px-2 py-1 text-xs rounded-full ${fidColor}`;

            // CLS (Cumulative Layout Shift)
            const cls = (Math.random() * 0.15).toFixed(2);
            document.getElementById('clsValue').textContent = cls;
            
            const clsStatus = cls <= 0.1 ? 'Good' : cls <= 0.25 ? 'Needs Improvement' : 'Poor';
            const clsColor = cls <= 0.1 ? 'bg-green-100 text-green-800' : cls <= 0.25 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800';
            document.getElementById('clsStatus').textContent = clsStatus;
            document.getElementById('clsStatus').className = `px-2 py-1 text-xs rounded-full ${clsColor}`;
        }

        function updatePerformanceMetrics(deviceType, connectionType) {
            const isMobile = deviceType === 'mobile';
            const connectionSpeed = {
                '3g': 0.4,
                '4g': 0.7,
                'cable': 1.0,
                'fiber': 1.3
            }[connectionType];

            // Load Time
            const baseLoadTime = isMobile ? 3.5 : 2.8;
            const loadTime = (baseLoadTime / connectionSpeed + Math.random() * 0.8).toFixed(1);
            document.getElementById('loadTime').textContent = loadTime + 's';

            // Page Size
            const pageSize = (1.0 + Math.random() * 1.5).toFixed(1);
            document.getElementById('pageSize').textContent = pageSize + 'MB';

            // Requests
            const requests = Math.round(35 + Math.random() * 30);
            document.getElementById('requests').textContent = requests;

            // TTFB (Time to First Byte)
            const baseTTFB = 200;
            const ttfb = Math.round(baseTTFB / connectionSpeed + Math.random() * 100);
            document.getElementById('ttfb').textContent = ttfb + 'ms';
        }

        function generateOptimizationOpportunities(score) {
            const opportunities = [
                { title: 'Optimize Images', impact: 'High', savings: '0.8s', description: 'Compress and resize images to reduce load time' },
                { title: 'Minify CSS', impact: 'Medium', savings: '0.3s', description: 'Remove unnecessary characters from CSS files' },
                { title: 'Enable Compression', impact: 'High', savings: '1.2s', description: 'Use gzip or brotli compression for text resources' },
                { title: 'Leverage Browser Caching', impact: 'Medium', savings: '0.5s', description: 'Set appropriate cache headers for static resources' },
                { title: 'Reduce JavaScript', impact: 'High', savings: '0.9s', description: 'Remove unused JavaScript and defer non-critical scripts' }
            ];

            // Show different opportunities based on score
            const numOpportunities = score >= 85 ? 2 : score >= 70 ? 3 : 5;
            const selectedOpportunities = opportunities.slice(0, numOpportunities);

            const container = document.getElementById('optimizationList');
            container.innerHTML = '';

            selectedOpportunities.forEach(opp => {
                const impactColor = opp.impact === 'High' ? 'text-red-600 bg-red-100' : 
                                  opp.impact === 'Medium' ? 'text-yellow-600 bg-yellow-100' : 
                                  'text-green-600 bg-green-100';

                const div = document.createElement('div');
                div.className = 'p-4 bg-white rounded-lg border shadow-sm';
                div.innerHTML = `
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-bold text-gray-900">${opp.title}</h4>
                        <div class="flex items-center gap-2">
                            <span class="px-2 py-1 text-xs rounded-full ${impactColor}">${opp.impact} Impact</span>
                            <span class="text-sm font-bold text-blue-600">${opp.savings}</span>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600">${opp.description}</p>
                `;
                container.appendChild(div);
            });
        }

        function updateResourceBreakdown() {
            // Generate realistic resource sizes
            const htmlSize = Math.round(30 + Math.random() * 40);
            const cssSize = Math.round(80 + Math.random() * 100);
            const jsSize = Math.round(200 + Math.random() * 400);
            const imageSize = Math.round(400 + Math.random() * 600);

            document.getElementById('htmlSize').textContent = htmlSize + 'KB';
            document.getElementById('cssSize').textContent = cssSize + 'KB';
            document.getElementById('jsSize').textContent = jsSize + 'KB';
            document.getElementById('imageSize').textContent = imageSize + 'KB';
        }

        function clearResults() {
            document.getElementById('websiteUrl').value = '';
            document.getElementById('resultsSection').classList.add('hidden');
            document.getElementById('loadingState').classList.add('hidden');
            testInProgress = false;
        }

        function exportReport() {
            if (document.getElementById('resultsSection').classList.contains('hidden')) {
                alert('Please run a speed test first to generate a report.');
                return;
            }
            
            alert('Speed Test Report Export:\n\n• PDF report with detailed analysis\n• Performance metrics and scores\n• Optimization recommendations\n• Historical comparison data\n• Shareable performance summary\n\nFull export functionality would be available in the complete version.');
        }

        function compareWebsites() {
            alert('Website Comparison Feature:\n\n• Compare multiple websites side-by-side\n• Benchmark against competitors\n• Industry performance averages\n• Detailed metric comparisons\n• Performance trend analysis\n\nThis advanced feature would be implemented in the full version.');
        }

        function scheduleTest() {
            alert('Scheduled Testing Feature:\n\n• Set up recurring speed tests\n• Monitor performance over time\n• Email alerts for performance issues\n• Historical performance tracking\n• Automated reporting\n\nScheduled monitoring would be available in the premium version.');
        }

        // Auto-focus on URL input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('websiteUrl').focus();
        });
    </script>
</body>
</html>
