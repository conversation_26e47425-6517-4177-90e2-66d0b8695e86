<?php
require_once '../config/config.php';
require_once '../config/cache.php';
require_once '../includes/wp-direct-posts.php';

$page_title = 'Blog';
$page_description = 'Stay updated with the latest SEO trends, link building strategies, and digital marketing insights from our expert team.';

// WordPress API Configuration
$wp_api_base = WP_API_BASE;

// DEBUG: Add debug output
if (isset($_GET['debug'])) {
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;'>";
    echo "<h3>🔍 DEBUG MODE</h3>";

    // Direct database test
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=jobzcsdn_login;charset=utf8", "jobzcsdn_login", "47G28{lIlsf-");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p style='color: green;'>✅ Database connected</p>";

        // Count all posts
        $count_stmt = $pdo->prepare("SELECT COUNT(*) FROM wp_posts");
        $count_stmt->execute();
        $total_posts = $count_stmt->fetchColumn();
        echo "<p><strong>Total wp_posts records:</strong> $total_posts</p>";

        // Count published posts
        $pub_stmt = $pdo->prepare("SELECT COUNT(*) FROM wp_posts WHERE post_status = 'publish' AND post_type = 'post'");
        $pub_stmt->execute();
        $published_posts = $pub_stmt->fetchColumn();
        echo "<p><strong>Published blog posts:</strong> $published_posts</p>";

        if ($published_posts > 0) {
            // Show some posts
            $posts_stmt = $pdo->prepare("SELECT ID, post_title, post_date FROM wp_posts WHERE post_status = 'publish' AND post_type = 'post' ORDER BY post_date DESC LIMIT 5");
            $posts_stmt->execute();
            $posts = $posts_stmt->fetchAll(PDO::FETCH_ASSOC);

            echo "<p><strong>Recent Posts:</strong></p>";
            foreach ($posts as $post) {
                echo "<p>• " . htmlspecialchars($post['post_title']) . " (ID: {$post['ID']}, Date: {$post['post_date']})</p>";
            }
        }

    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
    }

    echo "</div>";
}

// Function to fetch posts from WordPress
function fetchWordPressPosts($limit = 10, $offset = 0) {
    global $wp_api_base, $cache;

    $url = $wp_api_base . '/posts?per_page=' . $limit . '&offset=' . $offset . '&_embed';
    $cache_key = 'wp_posts_' . $limit . '_' . $offset;

    // Try to get from cache first
    $cached_posts = $cache->get($cache_key);
    if ($cached_posts !== false) {
        return $cached_posts;
    }

    // Debug: Show the URL being called
    echo "<!-- API URL: " . $url . " -->";

    // Use cURL instead of file_get_contents for better SSL handling
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; Blog Reader)');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For SSL issues
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // For SSL issues
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($curl_error)) {
            echo "<!-- cURL Error: " . $curl_error . " -->";
            echo "<!-- HTTP Code: " . $http_code . " -->";
            return [];
        }

        echo "<!-- HTTP Code: " . $http_code . " -->";
        echo "<!-- API Response: " . substr($response, 0, 1000) . "... -->";

    } else {
        // Fallback to file_get_contents with SSL context
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (compatible; Blog Reader)',
                'method' => 'GET',
                'header' => "Accept: application/json\r\n"
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ]);

        $response = @file_get_contents($url, false, $context);

        if ($response === false) {
            $error = error_get_last();
            echo "<!-- file_get_contents Error: " . print_r($error, true) . " -->";
            return [];
        }
    }

    $posts = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "<!-- JSON Error: " . json_last_error_msg() . " -->";
        return [];
    }

    $result = is_array($posts) ? $posts : [];

    // If API failed, try direct database method
    if (empty($result)) {
        echo "<!-- API failed, trying direct database method -->";
        $result = fetchWordPressPostsDirect($limit, $offset);
        echo "<!-- Direct DB result count: " . count($result) . " -->";

        // If direct method also fails, create hardcoded posts for now
        if (empty($result)) {
            echo "<!-- Direct DB also failed, using hardcoded posts -->";
            $result = [
                [
                    'id' => 1,
                    'title' => 'Advanced SEO Strategies for 2025',
                    'excerpt' => 'Discover the latest SEO techniques and strategies that will help your website rank higher in search engines this year.',
                    'content' => 'Learn about advanced SEO strategies including technical SEO, content optimization, and link building techniques...',
                    'date' => date('Y-m-d H:i:s'),
                    'author' => 'LoganixSEO Team',
                    'slug' => 'advanced-seo-strategies-2025',
                    'featured_image' => null
                ],
                [
                    'id' => 2,
                    'title' => 'Complete Link Building Guide for Beginners',
                    'excerpt' => 'A comprehensive guide to link building that covers everything from basics to advanced techniques.',
                    'content' => 'Link building is one of the most important aspects of SEO. Learn how to build high-quality backlinks...',
                    'date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                    'author' => 'LoganixSEO Team',
                    'slug' => 'link-building-guide-beginners',
                    'featured_image' => null
                ],
                [
                    'id' => 3,
                    'title' => 'Content Optimization Best Practices',
                    'excerpt' => 'Learn how to optimize your content for both search engines and users with these proven techniques.',
                    'content' => 'Content optimization is crucial for SEO success. Discover the best practices for creating SEO-friendly content...',
                    'date' => date('Y-m-d H:i:s', strtotime('-2 days')),
                    'author' => 'LoganixSEO Team',
                    'slug' => 'content-optimization-best-practices',
                    'featured_image' => null
                ],
                [
                    'id' => 4,
                    'title' => 'Technical SEO Checklist 2025',
                    'excerpt' => 'A complete technical SEO checklist to ensure your website is optimized for search engines.',
                    'content' => 'Technical SEO forms the foundation of your SEO strategy. Use this checklist to audit your website...',
                    'date' => date('Y-m-d H:i:s', strtotime('-3 days')),
                    'author' => 'LoganixSEO Team',
                    'slug' => 'technical-seo-checklist-2025',
                    'featured_image' => null
                ],
                [
                    'id' => 5,
                    'title' => 'Local SEO Tips for Small Businesses',
                    'excerpt' => 'Boost your local search rankings with these effective local SEO strategies for small businesses.',
                    'content' => 'Local SEO is essential for small businesses. Learn how to optimize your business for local search...',
                    'date' => date('Y-m-d H:i:s', strtotime('-4 days')),
                    'author' => 'LoganixSEO Team',
                    'slug' => 'local-seo-tips-small-businesses',
                    'featured_image' => null
                ]
            ];
        }
    }

    // Cache the result if it's valid
    if (!empty($result)) {
        $cache->set($cache_key, $result);
    }

    return $result;
}

// Function to get featured image URL
function getFeaturedImageUrl($post) {
    if (isset($post['_embedded']['wp:featuredmedia'][0]['source_url'])) {
        return $post['_embedded']['wp:featuredmedia'][0]['source_url'];
    }
    return null;
}

// Function to get author name
function getAuthorName($post) {
    if (isset($post['_embedded']['author'][0]['name'])) {
        $authorName = $post['_embedded']['author'][0]['name'];
        // Clean up author name
        if (strtolower($authorName) === 'admin') {
            return 'LoganixSEO Team';
        }
        return $authorName;
    }
    return 'LoganixSEO Team';
}



// Function to format date
function formatPostDate($date_string) {
    return date('M j, Y', strtotime($date_string));
}

// Function to get excerpt
function getPostExcerpt($post, $length = 150) {
    $content = strip_tags($post['content']['rendered']);
    if (strlen($content) > $length) {
        return substr($content, 0, $length) . '...';
    }
    return $content;
}

// Fetch posts
$posts = fetchWordPressPosts(6); // Fetch 6 posts for the grid
$featured_post = !empty($posts) ? $posts[0] : null; // First post as featured
$grid_posts = array_slice($posts, 1, 5); // Next 5 posts for grid
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo get_page_title($page_title); ?></title>
    <meta name="description" content="<?php echo get_page_description($page_description); ?>">
    <meta name="keywords" content="SEO blog, link building articles, digital marketing insights, SEO tips">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo get_page_title($page_title); ?>">
    <meta property="og:description" content="<?php echo get_page_description($page_description); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-8 lg:mb-10" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Latest <span class="text-yellow-300">SEO</span> Insights
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-10 lg:mb-12" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Stay updated with cutting-edge SEO strategies, link building techniques, and digital marketing trends.
                    </p>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6"></div>

        <!-- Featured Article - Magazine Style -->
        <section class="py-8 lg:py-12 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                <?php if ($featured_post): ?>
                <div class="rounded-xl shadow-lg overflow-hidden border-2" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC, #F1F5F9); border-color: #3B82F6;">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-0">
                        <!-- Featured Image -->
                        <div class="lg:col-span-2 relative">
                            <?php
                            $featured_image = getFeaturedImageUrl($featured_post);
                            if ($featured_image):
                            ?>
                                <img src="<?php echo htmlspecialchars($featured_image); ?>" alt="<?php echo htmlspecialchars($featured_post['title']['rendered']); ?>" class="w-full h-64 lg:h-96 object-cover">
                            <?php else: ?>
                                <div class="w-full h-64 lg:h-96 flex items-center justify-center" style="background: linear-gradient(135deg, #3B82F6, #1E40AF);">
                                    <svg class="w-24 h-24 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2.5 2.5 0 00-2.5-2.5H15"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>

                            <!-- Featured Badge -->
                            <div class="absolute top-4 left-4 z-10">
                                <span class="px-3 py-1 text-xs font-bold text-white rounded-full shadow-lg" style="background: linear-gradient(45deg, #F59E0B, #D97706); text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                                    FEATURED
                                </span>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="p-6 lg:p-8 flex flex-col justify-center">
                            <h2 class="text-xl lg:text-2xl font-bold text-gray-900 mb-4 leading-tight">
                                <?php echo htmlspecialchars($featured_post['title']['rendered']); ?>
                            </h2>
                            <p class="text-gray-600 mb-6 text-sm lg:text-base">
                                <?php echo getPostExcerpt($featured_post, 120); ?>
                            </p>

                            <!-- Author & Date -->
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-10 h-10 rounded-full flex items-center justify-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF);">
                                    <?php
                                    $featuredAuthorName = getAuthorName($featured_post);
                                    $featuredInitials = ($featuredAuthorName === 'LoganixSEO Team') ? 'LT' : strtoupper(substr($featuredAuthorName, 0, 1));
                                    ?>
                                    <span class="text-white font-bold text-sm"><?php echo $featuredInitials; ?></span>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900 text-base"><?php echo htmlspecialchars(getAuthorName($featured_post)); ?></div>
                                    <div class="text-gray-500 text-sm"><?php echo formatPostDate($featured_post['date']); ?></div>
                                </div>
                            </div>

                            <!-- Read More Button -->
                            <a href="../blog/<?php echo htmlspecialchars($featured_post['slug']); ?>.php" class="inline-flex items-center px-4 py-2 text-white font-medium rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg text-sm" style="background: linear-gradient(45deg, #3B82F6, #1E40AF);">
                                <span>Read Full Article</span>
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-gray-500 text-lg">No featured article available</div>
                    <p class="text-gray-400 mt-2">Please check back later for new content.</p>
                </div>
                <?php endif; ?>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6 lg:py-8"></div>

        <!-- Blog Articles Grid - Magazine Style -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 50%, #E2E8F0 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
                <div class="text-center mb-12 animate-on-scroll">
                    <h2 class="text-2xl lg:text-3xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Recent SEO Articles</h2>
                    <p class="text-gray-700 text-base lg:text-lg font-medium max-w-3xl mx-auto">
                        Discover actionable insights and proven strategies to boost your search rankings.
                    </p>

                    <!-- Cache Refresh Button (Development Only) -->
                    <?php if (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false): ?>
                    <div class="mt-4">
                        <a href="?nocache=1" class="inline-flex items-center px-4 py-2 text-sm text-blue-600 hover:text-blue-800 border border-blue-300 rounded-lg hover:bg-blue-50 transition-all duration-300">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh Posts
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php
                    $gradient_colors = [
                        'from-green-400 to-green-600',
                        'from-purple-400 to-purple-600',
                        'from-yellow-400 to-orange-500',
                        'from-red-400 to-pink-500',
                        'from-indigo-400 to-blue-600',
                        'from-teal-400 to-cyan-500'
                    ];

                    $svg_icons = [
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>',
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>',
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>',
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>',
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>',
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>'
                    ];

                    if (!empty($grid_posts)):
                        foreach ($grid_posts as $index => $post):
                            $gradient = $gradient_colors[$index % count($gradient_colors)];
                            $icon = $svg_icons[$index % count($svg_icons)];
                            $featured_image = getFeaturedImageUrl($post);
                    ?>
                    <article class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl overflow-hidden transform hover:scale-[1.02] transition-all duration-300 animate-on-scroll border border-gray-100 blog-card">
                        <!-- Image Section -->
                        <div class="relative overflow-hidden">
                            <?php if ($featured_image): ?>
                                <div class="h-52 overflow-hidden">
                                    <img src="<?php echo htmlspecialchars($featured_image); ?>" alt="<?php echo htmlspecialchars($post['title']['rendered']); ?>" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                                </div>
                            <?php else: ?>
                                <div class="h-52 bg-gradient-to-br <?php echo $gradient; ?> flex items-center justify-center relative overflow-hidden">
                                    <svg class="w-20 h-20 text-white z-10 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <?php echo $icon; ?>
                                    </svg>
                                    <div class="absolute inset-0 bg-black bg-opacity-10"></div>
                                    <!-- Decorative elements -->
                                    <div class="absolute top-4 right-4 w-8 h-8 rounded-full bg-white bg-opacity-20"></div>
                                    <div class="absolute bottom-4 left-4 w-6 h-6 rounded-full bg-white bg-opacity-15"></div>
                                </div>
                            <?php endif; ?>

                            <!-- Category Badge -->
                            <div class="absolute top-4 left-4">
                                <span class="px-3 py-1 text-xs font-semibold text-white rounded-full" style="background: rgba(59, 130, 246, 0.9); backdrop-filter: blur(10px);">
                                    SEO Tips
                                </span>
                            </div>
                        </div>

                        <!-- Content Section -->
                        <div class="p-6">
                            <!-- Title -->
                            <h3 class="text-xl font-bold text-gray-900 mb-3 leading-tight group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                                <a href="../blog/<?php echo htmlspecialchars($post['slug']); ?>.php" class="block">
                                    <?php echo htmlspecialchars($post['title']['rendered']); ?>
                                </a>
                            </h3>

                            <!-- Excerpt -->
                            <p class="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
                                <?php echo getPostExcerpt($post, 120); ?>
                            </p>

                            <!-- Author & Date -->
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF);">
                                        <?php
                                        $authorName = getAuthorName($post);
                                        $initials = ($authorName === 'LoganixSEO Team') ? 'LT' : strtoupper(substr($authorName, 0, 1));
                                        ?>
                                        <span class="text-xs font-bold text-white"><?php echo $initials; ?></span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars(getAuthorName($post)); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo formatPostDate($post['date']); ?></div>
                                    </div>
                                </div>

                                <!-- Reading time -->
                                <div class="flex items-center space-x-1 text-gray-500">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="text-xs">5 min read</span>
                                </div>
                            </div>

                            <!-- Read More Button -->
                            <div class="text-center">
                                <a href="../blog/<?php echo htmlspecialchars($post['slug']); ?>.php" class="inline-flex items-center justify-center w-full px-6 py-3 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg group" style="background: linear-gradient(45deg, #3B82F6, #1E40AF);">
                                    <span>Read Full Article</span>
                                    <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>
                    <?php
                        endforeach;
                    else:
                    ?>
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-500 text-lg">No blog posts available at the moment.</div>
                        <p class="text-gray-400 mt-2">Please check back later for new content.</p>
                    </div>
                    <?php endif; ?>


                </div>

                <!-- Load More Button -->
                <div class="text-center mt-12 animate-on-scroll">
                    <button class="text-white font-bold py-3 lg:py-4 px-6 lg:px-8 rounded-lg lg:rounded-xl transform hover:scale-105 transition-all duration-300 shadow-lg" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Load More Articles
                    </button>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6 lg:py-8"></div>

        <!-- Newsletter Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
                <div class="max-w-3xl mx-auto animate-on-scroll">
                    <h2 class="text-2xl lg:text-3xl font-bold text-white mb-6" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Stay Updated with SEO Trends
                    </h2>
                    <p class="text-lg text-white mb-8" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Get the latest SEO insights, link building strategies, and digital marketing tips delivered to your inbox.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                        <input type="email" placeholder="Enter your email address" class="px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 flex-1 text-gray-900">
                        <button class="bg-white text-gray-900 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Subscribe Now
                        </button>
                    </div>
                    <p class="text-white text-sm mt-4 opacity-90">
                        Join 10,000+ SEO professionals. Unsubscribe anytime.
                    </p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
</body>
</html>

<?php
// Check for new posts and update sitemap if needed
$current_posts = fetchWordPressPosts(1, 0); // Get latest post
if (!empty($current_posts)) {
    $latest_post = $current_posts[0];
    $post_date = strtotime($latest_post['date']);
    $cache_key = 'last_sitemap_update_check';
    $last_check = $cache->get($cache_key);

    // If this is a new post (published in last 24 hours) and we haven't updated sitemap recently
    if ($post_date > (time() - 86400) && ($last_check === false || $last_check < $post_date)) {
        require_once '../includes/sitemap-updater.php';
        sitemapUpdateForNewPost($latest_post['title']['rendered']);
        $cache->set($cache_key, time(), 3600); // Cache for 1 hour
    }
}
?>
