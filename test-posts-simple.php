<?php
echo "<h1>Simple Posts Test</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=jobzcsdn_login", "jobzcsdn_login", "47G28{lIlsf-");
    echo "<p style='color: green;'>Database connected</p>";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM wp_posts WHERE post_status = 'publish' AND post_type = 'post'");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    echo "<p><strong>Published posts: $count</strong></p>";
    
    if ($count == 0) {
        echo "<p style='color: red;'>NO POSTS IN DATABASE!</p>";
        echo "<p>You need to create posts in WordPress admin first.</p>";
        echo "<p><a href='https://dribs.xyz/wp-admin/post-new.php'>Create New Post</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
