# PowerShell script to copy LoganixSEO project to Desktop
Write-Host "Copying LoganixSEO project to Desktop..." -ForegroundColor Green

# Get current directory
$sourceDir = Get-Location
Write-Host "Source: $sourceDir" -ForegroundColor Yellow

# Set destination
$desktopPath = [Environment]::GetFolderPath("Desktop")
$destDir = Join-Path $desktopPath "loganixseo.com"
Write-Host "Destination: $destDir" -ForegroundColor Yellow

# Create destination directory if it doesn't exist
if (!(Test-Path $destDir)) {
    New-Item -ItemType Directory -Path $destDir -Force | Out-Null
    Write-Host "Created destination directory" -ForegroundColor Green
}

try {
    # Copy all files and folders
    Copy-Item -Path "$sourceDir\*" -Destination $destDir -Recurse -Force
    Write-Host ""
    Write-Host "✅ Project copied successfully to Desktop!" -ForegroundColor Green
    Write-Host "Location: $destDir" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "You can now:" -ForegroundColor Yellow
    Write-Host "1. Upload this folder to your live hosting" -ForegroundColor White
    Write-Host "2. Run fix-wordpress-urls.php after upload" -ForegroundColor White
    Write-Host "3. Update WordPress permalinks" -ForegroundColor White
    Write-Host ""
    
    # Show folder contents
    $fileCount = (Get-ChildItem $destDir -Recurse | Measure-Object).Count
    Write-Host "Total files copied: $fileCount" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error copying files: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
