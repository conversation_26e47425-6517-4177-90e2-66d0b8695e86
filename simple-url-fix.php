<?php
/**
 * Simple WordPress URL Fixer - No Complex Code
 */

echo "<h1>Simple WordPress URL Fix</h1>";

// Database credentials
$db_name = 'jobzcsdn_login';
$db_user = 'jobzcsdn_login';
$db_pass = '47G28{lIlsf-';
$db_host = 'localhost';

// New URLs
$new_site_url = 'https://dribs.xyz/wp';
$new_home_url = 'https://dribs.xyz/wp';

echo "<p><strong>New Site URL:</strong> $new_site_url</p>";
echo "<p><strong>New Home URL:</strong> $new_home_url</p>";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    echo "<p style='color: green;'>✅ Database connected</p>";
    
    // Update siteurl
    $stmt1 = $pdo->prepare("UPDATE wp_options SET option_value = ? WHERE option_name = 'siteurl'");
    $result1 = $stmt1->execute([$new_site_url]);
    
    if ($result1) {
        echo "<p style='color: green;'>✅ Updated siteurl</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to update siteurl</p>";
    }
    
    // Update home
    $stmt2 = $pdo->prepare("UPDATE wp_options SET option_value = ? WHERE option_name = 'home'");
    $result2 = $stmt2->execute([$new_home_url]);
    
    if ($result2) {
        echo "<p style='color: green;'>✅ Updated home URL</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to update home URL</p>";
    }
    
    echo "<h2>✅ URL Fix Complete!</h2>";
    echo "<p>Now try:</p>";
    echo "<ul>";
    echo "<li><a href='https://dribs.xyz/wp-admin/'>WordPress Admin</a></li>";
    echo "<li><a href='https://dribs.xyz/pages/blog.php'>Blog Page</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
