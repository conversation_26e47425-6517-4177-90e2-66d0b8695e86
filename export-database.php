<?php
/**
 * Database Export Script for XAMPP to Hosting Migration
 */

echo "<h1>Database Export for Hosting Migration</h1>";

// XAMPP Database credentials
$xampp_db_name = 'xseo';
$xampp_db_user = 'root';
$xampp_db_pass = '';
$xampp_db_host = 'localhost';

echo "<h2>XAMPP Database Export:</h2>";

try {
    $pdo = new PDO("mysql:host=$xampp_db_host;dbname=$xampp_db_name;charset=utf8", $xampp_db_user, $xampp_db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Connected to XAMPP database</p>";
    
    // Check WordPress tables and data
    $tables_check = [
        'wp_posts' => "SELECT COUNT(*) FROM wp_posts WHERE post_status = 'publish' AND post_type = 'post'",
        'wp_options' => "SELECT COUNT(*) FROM wp_options",
        'wp_users' => "SELECT COUNT(*) FROM wp_users"
    ];
    
    echo "<h3>Database Content:</h3>";
    foreach ($tables_check as $table => $query) {
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute();
            $count = $stmt->fetchColumn();
            echo "<p>📊 $table: $count records</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ $table: Not found</p>";
        }
    }
    
    // Generate export instructions
    echo "<h2>📥 Export Instructions:</h2>";
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Method 1: phpMyAdmin (Recommended)</h3>";
    echo "<ol>";
    echo "<li>Open: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
    echo "<li>Select database: <strong>$xampp_db_name</strong></li>";
    echo "<li>Click 'Export' tab</li>";
    echo "<li>Select 'Quick' export method</li>";
    echo "<li>Format: SQL</li>";
    echo "<li>Click 'Go' to download .sql file</li>";
    echo "</ol>";
    
    echo "<h3>Method 2: Command Line</h3>";
    echo "<pre style='background: #333; color: #fff; padding: 10px;'>";
    echo "mysqldump -u root -p $xampp_db_name > loganixseo_backup.sql";
    echo "</pre>";
    echo "</div>";
    
    // Show current URLs that need to be changed
    echo "<h2>🔗 URLs to be Updated:</h2>";
    try {
        $url_stmt = $pdo->prepare("SELECT option_name, option_value FROM wp_options WHERE option_name IN ('siteurl', 'home')");
        $url_stmt->execute();
        $urls = $url_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Option</th><th>Current Value (XAMPP)</th><th>New Value (Hosting)</th></tr>";
        
        foreach ($urls as $url) {
            $new_value = str_replace('http://localhost/loganixseo.com/wp', 'https://dribs.xyz/wp', $url['option_value']);
            echo "<tr>";
            echo "<td>{$url['option_name']}</td>";
            echo "<td>{$url['option_value']}</td>";
            echo "<td style='color: green;'>$new_value</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p>Could not fetch URL options</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Could not connect to XAMPP database</p>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Make sure XAMPP MySQL is running</p>";
}

echo "<h2>📤 Next Steps:</h2>";
echo "<ol>";
echo "<li><strong>Export database</strong> using phpMyAdmin</li>";
echo "<li><strong>Upload .sql file</strong> to hosting cPanel > phpMyAdmin</li>";
echo "<li><strong>Import database</strong> into jobzcsdn_login database</li>";
echo "<li><strong>Run fix-wordpress-urls.php</strong> on hosting</li>";
echo "<li><strong>Test blog page</strong></li>";
echo "</ol>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
