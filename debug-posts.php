<?php
/**
 * Debug Posts Display Issue
 */

echo "<h1>🔍 Posts Debug Tool</h1>";

// Database credentials
$db_name = 'jobzcsdn_login';
$db_user = 'jobzcsdn_login';
$db_pass = '47G28{lIlsf-';
$db_host = 'localhost';

echo "<h2>1. Database Connection Test:</h2>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
    
    // Check all posts
    echo "<h2>2. All Posts in Database:</h2>";
    $all_posts_stmt = $pdo->prepare("SELECT ID, post_title, post_status, post_type, post_date FROM wp_posts ORDER BY ID DESC");
    $all_posts_stmt->execute();
    $all_posts = $all_posts_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total records in wp_posts:</strong> " . count($all_posts) . "</p>";
    
    if (!empty($all_posts)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Title</th><th>Status</th><th>Type</th><th>Date</th></tr>";
        foreach ($all_posts as $post) {
            $row_color = ($post['post_status'] === 'publish' && $post['post_type'] === 'post') ? 'background: #e8f5e8;' : '';
            echo "<tr style='$row_color'>";
            echo "<td>{$post['ID']}</td>";
            echo "<td>" . htmlspecialchars($post['post_title']) . "</td>";
            echo "<td>{$post['post_status']}</td>";
            echo "<td>{$post['post_type']}</td>";
            echo "<td>{$post['post_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check published posts specifically
    echo "<h2>3. Published Blog Posts:</h2>";
    $published_stmt = $pdo->prepare("SELECT ID, post_title, post_content, post_date FROM wp_posts WHERE post_status = 'publish' AND post_type = 'post' ORDER BY post_date DESC");
    $published_stmt->execute();
    $published_posts = $published_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Published blog posts:</strong> " . count($published_posts) . "</p>";
    
    if (empty($published_posts)) {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3 style='color: red;'>❌ No Published Posts Found!</h3>";
        echo "<p>This is why your blog page shows 'No blog posts available'</p>";
        echo "<p><strong>Solution:</strong></p>";
        echo "<ol>";
        echo "<li>Go to <a href='https://dribs.xyz/wp-admin/' target='_blank'>WordPress Admin</a></li>";
        echo "<li>Click 'Posts' → 'Add New'</li>";
        echo "<li>Create and publish some blog posts</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3 style='color: green;'>✅ Published Posts Found!</h3>";
        foreach ($published_posts as $post) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
            echo "<h4>" . htmlspecialchars($post['post_title']) . "</h4>";
            echo "<p><strong>Date:</strong> {$post['post_date']}</p>";
            echo "<p><strong>Content Preview:</strong> " . htmlspecialchars(substr(strip_tags($post['post_content']), 0, 100)) . "...</p>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // Test the direct fetch function
    echo "<h2>4. Testing Direct Fetch Function:</h2>";
    
    // Include the direct posts function
    include_once 'includes/wp-direct-posts.php';
    
    $direct_posts = fetchWordPressPostsDirect(10, 0);
    echo "<p><strong>Direct fetch result:</strong> " . count($direct_posts) . " posts</p>";
    
    if (!empty($direct_posts)) {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ Direct fetch working! Posts found:</h4>";
        foreach ($direct_posts as $post) {
            echo "<p>• " . htmlspecialchars($post['title']) . "</p>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Direct fetch failed!</h4>";
        echo "<p>The fetchWordPressPostsDirect function is not working properly.</p>";
        echo "</div>";
    }
    
    // Check WordPress options
    echo "<h2>5. WordPress Configuration:</h2>";
    $options_stmt = $pdo->prepare("SELECT option_name, option_value FROM wp_options WHERE option_name IN ('siteurl', 'home', 'blogname', 'blogdescription')");
    $options_stmt->execute();
    $options = $options_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($options as $option) {
        echo "<p><strong>{$option['option_name']}:</strong> " . htmlspecialchars($option['option_value']) . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database connection failed!</p>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>6. Next Steps:</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px;'>";
echo "<ol>";
echo "<li>If no published posts: Create posts in WordPress admin</li>";
echo "<li>If posts exist but direct fetch fails: Fix the fetch function</li>";
echo "<li>If everything looks good: Clear cache and test blog page</li>";
echo "</ol>";
echo "</div>";

echo "<h2>7. Quick Actions:</h2>";
echo "<p><a href='https://dribs.xyz/wp-admin/post-new.php' target='_blank' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>➕ Create New Post</a></p>";
echo "<p><a href='https://dribs.xyz/pages/blog.php?nocache=1' target='_blank' style='background: #00a32a; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>🔄 Test Blog Page</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { width: 100%; border-collapse: collapse; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
</style>
