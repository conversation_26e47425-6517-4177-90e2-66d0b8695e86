<?php
session_start();
require_once '../config/config.php';

$page_title = 'Free Readability Checker Tool - Analyze Content Readability Score | LoganixSEO';
$page_description = 'Check your content readability with our free readability checker. Analyze <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Fog, and other readability scores to improve content accessibility and SEO performance.';
$page_keywords = 'readability checker, content readability, flesch kincaid score, gunning fog index, readability analysis, content optimization, seo readability, text analysis';

// Get current page for navigation
$current_page = 'tools';

$readability_results = [];
$input_text = '';

if ($_POST && !empty($_POST['text'])) {
    $input_text = trim($_POST['text']);
    
    // Calculate readability scores
    $readability_results = analyzeReadability($input_text);
}

function analyzeReadability($text) {
    // Basic text statistics
    $sentences = preg_split('/[.!?]+/', $text, -1, PREG_SPLIT_NO_EMPTY);
    $sentence_count = count($sentences);
    
    $words = str_word_count($text, 1);
    $word_count = count($words);
    
    $syllable_count = 0;
    foreach ($words as $word) {
        $syllable_count += countSyllables(strtolower($word));
    }
    
    $character_count = strlen(preg_replace('/\s+/', '', $text));
    $character_count_with_spaces = strlen($text);
    
    // Avoid division by zero
    if ($sentence_count == 0 || $word_count == 0) {
        return [
            'error' => 'Please provide text with complete sentences for analysis.'
        ];
    }
    
    $avg_sentence_length = $word_count / $sentence_count;
    $avg_syllables_per_word = $syllable_count / $word_count;
    
    // Flesch Reading Ease Score
    $flesch_ease = 206.835 - (1.015 * $avg_sentence_length) - (84.6 * $avg_syllables_per_word);
    $flesch_ease = max(0, min(100, $flesch_ease)); // Clamp between 0-100
    
    // Flesch-Kincaid Grade Level
    $flesch_kincaid_grade = (0.39 * $avg_sentence_length) + (11.8 * $avg_syllables_per_word) - 15.59;
    $flesch_kincaid_grade = max(0, $flesch_kincaid_grade);
    
    // Gunning Fog Index
    $complex_words = 0;
    foreach ($words as $word) {
        if (countSyllables(strtolower($word)) >= 3) {
            $complex_words++;
        }
    }
    $complex_word_percentage = ($complex_words / $word_count) * 100;
    $gunning_fog = 0.4 * ($avg_sentence_length + $complex_word_percentage);
    
    // SMOG Index (simplified)
    $smog_index = 1.0430 * sqrt($complex_words * (30 / $sentence_count)) + 3.1291;
    $smog_index = max(0, $smog_index);
    
    // Automated Readability Index
    $ari = (4.71 * ($character_count / $word_count)) + (0.5 * ($word_count / $sentence_count)) - 21.43;
    $ari = max(0, $ari);
    
    // Determine readability level
    $readability_level = getReadabilityLevel($flesch_ease);
    $grade_level = getGradeLevel($flesch_kincaid_grade);
    
    return [
        'statistics' => [
            'characters' => $character_count,
            'characters_with_spaces' => $character_count_with_spaces,
            'words' => $word_count,
            'sentences' => $sentence_count,
            'syllables' => $syllable_count,
            'avg_sentence_length' => round($avg_sentence_length, 2),
            'avg_syllables_per_word' => round($avg_syllables_per_word, 2),
            'complex_words' => $complex_words,
            'complex_word_percentage' => round($complex_word_percentage, 2)
        ],
        'scores' => [
            'flesch_ease' => round($flesch_ease, 1),
            'flesch_kincaid_grade' => round($flesch_kincaid_grade, 1),
            'gunning_fog' => round($gunning_fog, 1),
            'smog_index' => round($smog_index, 1),
            'ari' => round($ari, 1)
        ],
        'readability_level' => $readability_level,
        'grade_level' => $grade_level,
        'recommendations' => getRecommendations($flesch_ease, $avg_sentence_length, $complex_word_percentage)
    ];
}

function countSyllables($word) {
    $word = strtolower($word);
    $word = preg_replace('/[^a-z]/', '', $word);
    
    if (strlen($word) <= 3) return 1;
    
    $word = preg_replace('/(?:[^laeiouy]es|ed|[^laeiouy]e)$/', '', $word);
    $word = preg_replace('/^y/', '', $word);
    $matches = preg_match_all('/[aeiouy]{1,2}/', $word);
    
    return max(1, $matches);
}

function getReadabilityLevel($flesch_score) {
    if ($flesch_score >= 90) return ['level' => 'Very Easy', 'color' => 'green'];
    if ($flesch_score >= 80) return ['level' => 'Easy', 'color' => 'green'];
    if ($flesch_score >= 70) return ['level' => 'Fairly Easy', 'color' => 'blue'];
    if ($flesch_score >= 60) return ['level' => 'Standard', 'color' => 'yellow'];
    if ($flesch_score >= 50) return ['level' => 'Fairly Difficult', 'color' => 'orange'];
    if ($flesch_score >= 30) return ['level' => 'Difficult', 'color' => 'red'];
    return ['level' => 'Very Difficult', 'color' => 'red'];
}

function getGradeLevel($grade) {
    if ($grade <= 6) return 'Elementary School';
    if ($grade <= 8) return 'Middle School';
    if ($grade <= 12) return 'High School';
    if ($grade <= 16) return 'College Level';
    return 'Graduate Level';
}

function getRecommendations($flesch_score, $avg_sentence_length, $complex_word_percentage) {
    $recommendations = [];
    
    if ($flesch_score < 60) {
        $recommendations[] = 'Consider simplifying your content to improve readability.';
    }
    
    if ($avg_sentence_length > 20) {
        $recommendations[] = 'Try to shorten your sentences. Aim for 15-20 words per sentence.';
    }
    
    if ($complex_word_percentage > 15) {
        $recommendations[] = 'Reduce complex words. Use simpler alternatives where possible.';
    }
    
    if ($flesch_score >= 70) {
        $recommendations[] = 'Great! Your content has good readability for most audiences.';
    }
    
    if (empty($recommendations)) {
        $recommendations[] = 'Your content readability is within acceptable ranges.';
    }
    
    return $recommendations;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/readability-checker-tool.jpg">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">
    <meta name="twitter:image" content="<?php echo SITE_URL; ?>/assets/images/readability-checker-tool.jpg">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Readability Checker Tool",
        "description": "<?php echo $page_description; ?>",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "SEO Tool",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "LoganixSEO",
            "url": "<?php echo SITE_URL; ?>"
        },
        "featureList": [
            "Flesch Reading Ease Score",
            "Flesch-Kincaid Grade Level",
            "Gunning Fog Index",
            "SMOG Index",
            "Automated Readability Index",
            "Content recommendations",
            "Free to use"
        ]
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-16 lg:py-20 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <div class="mb-6">
                        <span class="text-4xl lg:text-5xl">📖</span>
                    </div>
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-6 lg:mb-8" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Free Readability Checker Tool
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-8 lg:mb-10" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Analyze your content readability with multiple scoring methods. Improve accessibility and SEO performance with detailed insights.
                    </p>
                    
                    <!-- Breadcrumb -->
                    <nav class="text-white/80 text-sm mb-8">
                        <a href="../index.php" class="hover:text-white">Home</a>
                        <span class="mx-2">›</span>
                        <a href="../pages/tools.php" class="hover:text-white">SEO Tools</a>
                        <span class="mx-2">›</span>
                        <span class="text-white">Readability Checker</span>
                    </nav>
                </div>
            </div>
        </section>

        <!-- Tool Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 50%, #E2E8F0 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
                <div class="rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <form method="POST" class="space-y-6">
                        <div>
                            <label for="text" class="block text-sm font-bold text-gray-900 mb-2">Paste Your Content *</label>
                            <textarea id="text" name="text" rows="12" required 
                                      class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors duration-300 resize-vertical" 
                                      placeholder="Paste your article, blog post, or any content here to analyze its readability..."><?php echo htmlspecialchars($input_text); ?></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="px-8 py-4 text-lg font-bold text-white rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 inline-flex items-center space-x-2" style="background: linear-gradient(45deg, #3B82F6, #1E40AF);">
                                <span>Analyze Readability</span>
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Results Section -->
                <?php if (!empty($readability_results)): ?>
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <?php if (isset($readability_results['error'])): ?>
                        <div class="text-center p-6 bg-red-50 border border-red-200 rounded-lg">
                            <div class="text-red-600 font-semibold mb-2">Analysis Error</div>
                            <div class="text-red-500"><?php echo htmlspecialchars($readability_results['error']); ?></div>
                        </div>
                    <?php else: ?>
                        <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                            Readability Analysis Results
                        </h2>

                        <!-- Overall Score -->
                        <div class="text-center mb-8 p-6 rounded-lg border-2" style="background-color: <?php echo $readability_results['readability_level']['color'] === 'green' ? '#F0FDF4' : ($readability_results['readability_level']['color'] === 'blue' ? '#EFF6FF' : ($readability_results['readability_level']['color'] === 'yellow' ? '#FEFCE8' : ($readability_results['readability_level']['color'] === 'orange' ? '#FFF7ED' : '#FEF2F2'))); ?>; border-color: <?php echo $readability_results['readability_level']['color'] === 'green' ? '#22C55E' : ($readability_results['readability_level']['color'] === 'blue' ? '#3B82F6' : ($readability_results['readability_level']['color'] === 'yellow' ? '#EAB308' : ($readability_results['readability_level']['color'] === 'orange' ? '#F97316' : '#EF4444'))); ?>;">
                            <div class="text-3xl font-bold mb-2" style="color: <?php echo $readability_results['readability_level']['color'] === 'green' ? '#22C55E' : ($readability_results['readability_level']['color'] === 'blue' ? '#3B82F6' : ($readability_results['readability_level']['color'] === 'yellow' ? '#EAB308' : ($readability_results['readability_level']['color'] === 'orange' ? '#F97316' : '#EF4444'))); ?>;">
                                <?php echo $readability_results['scores']['flesch_ease']; ?>/100
                            </div>
                            <div class="text-lg font-semibold text-gray-800">
                                <?php echo $readability_results['readability_level']['level']; ?>
                            </div>
                            <div class="text-sm text-gray-600 mt-2">
                                Grade Level: <?php echo $readability_results['grade_level']; ?>
                            </div>
                        </div>

                        <!-- Detailed Scores -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600"><?php echo $readability_results['scores']['flesch_ease']; ?></div>
                                <div class="text-sm text-blue-800">Flesch Reading Ease</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600"><?php echo $readability_results['scores']['flesch_kincaid_grade']; ?></div>
                                <div class="text-sm text-green-800">Flesch-Kincaid Grade</div>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div class="text-2xl font-bold text-purple-600"><?php echo $readability_results['scores']['gunning_fog']; ?></div>
                                <div class="text-sm text-purple-800">Gunning Fog Index</div>
                            </div>
                            <div class="text-center p-4 bg-orange-50 rounded-lg">
                                <div class="text-2xl font-bold text-orange-600"><?php echo $readability_results['scores']['smog_index']; ?></div>
                                <div class="text-sm text-orange-800">SMOG Index</div>
                            </div>
                            <div class="text-center p-4 bg-red-50 rounded-lg">
                                <div class="text-2xl font-bold text-red-600"><?php echo $readability_results['scores']['ari']; ?></div>
                                <div class="text-sm text-red-800">Automated Readability</div>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <div class="text-2xl font-bold text-gray-600"><?php echo $readability_results['statistics']['words']; ?></div>
                                <div class="text-sm text-gray-800">Total Words</div>
                            </div>
                        </div>

                        <!-- Text Statistics -->
                        <div class="bg-gray-50 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Text Statistics</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                    <span class="font-medium">Characters:</span>
                                    <span class="text-gray-600"><?php echo number_format($readability_results['statistics']['characters']); ?></span>
                                </div>
                                <div>
                                    <span class="font-medium">Words:</span>
                                    <span class="text-gray-600"><?php echo number_format($readability_results['statistics']['words']); ?></span>
                                </div>
                                <div>
                                    <span class="font-medium">Sentences:</span>
                                    <span class="text-gray-600"><?php echo number_format($readability_results['statistics']['sentences']); ?></span>
                                </div>
                                <div>
                                    <span class="font-medium">Syllables:</span>
                                    <span class="text-gray-600"><?php echo number_format($readability_results['statistics']['syllables']); ?></span>
                                </div>
                                <div>
                                    <span class="font-medium">Avg Sentence Length:</span>
                                    <span class="text-gray-600"><?php echo $readability_results['statistics']['avg_sentence_length']; ?> words</span>
                                </div>
                                <div>
                                    <span class="font-medium">Avg Syllables/Word:</span>
                                    <span class="text-gray-600"><?php echo $readability_results['statistics']['avg_syllables_per_word']; ?></span>
                                </div>
                                <div>
                                    <span class="font-medium">Complex Words:</span>
                                    <span class="text-gray-600"><?php echo $readability_results['statistics']['complex_words']; ?></span>
                                </div>
                                <div>
                                    <span class="font-medium">Complex Word %:</span>
                                    <span class="text-gray-600"><?php echo $readability_results['statistics']['complex_word_percentage']; ?>%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Recommendations -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-yellow-800 mb-4">📝 Recommendations:</h3>
                            <ul class="space-y-2">
                                <?php foreach ($readability_results['recommendations'] as $recommendation): ?>
                                <li class="flex items-start space-x-2">
                                    <span class="text-yellow-600 mt-1">•</span>
                                    <span class="text-yellow-700"><?php echo htmlspecialchars($recommendation); ?></span>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>

                        <div class="text-center">
                            <button onclick="downloadReport()" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-300">
                                Download Report
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Tool Description -->
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        About Our Readability Checker Tool
                    </h2>

                    <div class="prose max-w-none text-gray-700 leading-relaxed">
                        <p class="mb-4">
                            Our free Readability Checker is a comprehensive content analysis tool that evaluates text accessibility using multiple industry-standard readability formulas including Flesch Reading Ease, Flesch-Kincaid Grade Level, Gunning Fog Index, SMOG Index, and Automated Readability Index. This essential tool helps content creators, marketers, and SEO professionals optimize their content for better user engagement and search engine performance.
                        </p>

                        <p class="mb-4">
                            The tool integrates seamlessly with your <a href="../services/content-writing.php" class="text-blue-600 hover:text-blue-800 font-semibold">content optimization strategy</a>, providing detailed insights into sentence complexity, vocabulary difficulty, and overall readability scores. Search engines favor content that is accessible to a broad audience, making readability a crucial factor in SEO rankings and user experience optimization.
                        </p>

                        <p class="mb-4">
                            Perfect for <a href="../services/seo-services.php" class="text-blue-600 hover:text-blue-800 font-semibold">SEO content optimization</a>, our checker analyzes text statistics including word count, sentence length, syllable distribution, and complex word usage. The tool provides actionable recommendations to improve content accessibility, helping you create content that resonates with your target audience while maintaining search engine visibility.
                        </p>

                        <p>
                            Combined with our <a href="../services/technical-seo.php" class="text-blue-600 hover:text-blue-800 font-semibold">technical SEO services</a>, readability optimization ensures your content meets accessibility standards and provides excellent user experience across all devices and reading levels. Use this tool to maintain consistent content quality and improve engagement metrics that positively impact search rankings.
                        </p>
                    </div>
                </div>

                <!-- FAQ Section -->
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        Frequently Asked Questions
                    </h2>

                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                What is a good readability score?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                For web content, aim for a Flesch Reading Ease score of 60-70 (Standard level). Scores of 70+ are considered good for general audiences, while 80+ is excellent for broad accessibility.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                How does readability affect SEO?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Better readability improves user engagement metrics like time on page and bounce rate, which are SEO ranking factors. Accessible content also reaches wider audiences and improves overall user experience.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                What's the difference between readability formulas?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Each formula uses different factors: Flesch focuses on sentence length and syllables, Gunning Fog emphasizes complex words, SMOG targets complex words in shorter samples, and ARI uses character counts instead of syllables.
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg">
                            <button class="w-full px-6 py-4 text-left font-semibold text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-300" onclick="toggleFAQ(this)">
                                How can I improve my content's readability?
                                <svg class="w-5 h-5 float-right mt-1 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="px-6 pb-4 text-gray-600 hidden">
                                Use shorter sentences (15-20 words), choose simpler words when possible, break up long paragraphs, use active voice, and include transition words. Our tool provides specific recommendations based on your content analysis.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Tools -->
                <div class="mt-12 rounded-xl shadow-lg p-8 border-2 animate-on-scroll" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC); border-color: #E2E8F0;">
                    <h2 class="text-2xl font-bold mb-6 text-center" style="background: linear-gradient(45deg, #3B82F6, #1E40AF); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                        Related SEO Tools
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <a href="keyword-density-analyzer.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Keyword Density Analyzer</h3>
                            <p class="text-gray-600 text-sm">Analyze keyword frequency in your content</p>
                        </a>

                        <a href="seo-calculator.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">SEO Calculator</h3>
                            <p class="text-gray-600 text-sm">Calculate SEO performance scores</p>
                        </a>

                        <a href="blog-idea-generator.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Blog Idea Generator</h3>
                            <p class="text-gray-600 text-sm">Generate creative content ideas</p>
                        </a>

                        <a href="meta-tag-generator.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Meta Tag Generator</h3>
                            <p class="text-gray-600 text-sm">Generate SEO-optimized meta tags</p>
                        </a>

                        <a href="content-optimizer.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Content Optimizer</h3>
                            <p class="text-gray-600 text-sm">Optimize content for better SEO</p>
                        </a>

                        <a href="../pages/contact.php" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Contact LoganixSEO</h3>
                            <p class="text-gray-600 text-sm">Get professional content optimization services</p>
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>

    <script>
        function downloadReport() {
            const results = <?php echo json_encode($readability_results); ?>;
            const originalText = "<?php echo addslashes($input_text); ?>";

            let content = `Readability Analysis Report\n`;
            content += `Generated on: ${new Date().toLocaleDateString()}\n\n`;
            content += `CONTENT ANALYSIS:\n`;
            content += `Text Length: ${results.statistics.words} words\n`;
            content += `Sentences: ${results.statistics.sentences}\n`;
            content += `Characters: ${results.statistics.characters}\n\n`;

            content += `READABILITY SCORES:\n`;
            content += `Flesch Reading Ease: ${results.scores.flesch_ease}/100 (${results.readability_level.level})\n`;
            content += `Flesch-Kincaid Grade: ${results.scores.flesch_kincaid_grade}\n`;
            content += `Gunning Fog Index: ${results.scores.gunning_fog}\n`;
            content += `SMOG Index: ${results.scores.smog_index}\n`;
            content += `Automated Readability Index: ${results.scores.ari}\n\n`;

            content += `TEXT STATISTICS:\n`;
            content += `Average Sentence Length: ${results.statistics.avg_sentence_length} words\n`;
            content += `Average Syllables per Word: ${results.statistics.avg_syllables_per_word}\n`;
            content += `Complex Words: ${results.statistics.complex_words} (${results.statistics.complex_word_percentage}%)\n\n`;

            if (results.recommendations && results.recommendations.length > 0) {
                content += `RECOMMENDATIONS:\n`;
                results.recommendations.forEach((rec, index) => {
                    content += `${index + 1}. ${rec}\n`;
                });
            }

            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'readability-report.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function toggleFAQ(button) {
            const content = button.nextElementSibling;
            const icon = button.querySelector('svg');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }
    </script>
</body>
</html>
