<?php
require_once '../config/config.php';

$page_title = 'UTM Builder - Free UTM Parameter Generator for Campaign Tracking';
$page_description = 'Generate UTM parameters for Google Analytics campaign tracking. Create UTM codes for email, social media, and advertising campaigns with our free UTM builder.';
$page_keywords = 'utm builder, utm generator, utm parameters, campaign tracking, google analytics, utm codes, marketing analytics, campaign urls';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "UTM Builder",
        "description": "Generate UTM parameters for Google Analytics campaign tracking. Create UTM codes for email, social media, and advertising campaigns with our free UTM builder.",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "Marketing Tool",
        "operatingSystem": "Any",
        "permissions": "browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "SEO Agency",
            "url": "http://localhost/test"
        }
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main class="pt-20 lg:pt-24">
        <!-- Hero Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-8 lg:mb-10" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        UTM Builder
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-10 lg:mb-12" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Generate UTM parameters for accurate campaign tracking and Google Analytics integration.
                    </p>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6"></div>

        <!-- Tool Section -->
        <section class="py-8 lg:py-12 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl">
                <div class="rounded-xl shadow-lg p-6 lg:p-8 border-2" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC, #F1F5F9); border-color: #F59E0B;">
                    <div class="text-center mb-6 lg:mb-8">
                        <h2 class="text-2xl lg:text-3xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #F59E0B, #D97706); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Professional UTM Parameter Generator</h2>
                        <p class="text-gray-700 text-base lg:text-lg font-medium">Create trackable URLs for marketing campaigns and analytics</p>
                    </div>

                    <!-- Tool Interface -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                        <!-- Input Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <!-- Website URL -->
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Website URL *</label>
                                <input type="url" id="websiteUrl" class="w-full px-3 py-2 border-2 rounded-lg focus:outline-none text-gray-800 text-sm" style="border-color: #FDE68A; background: linear-gradient(45deg, #FFFBEB, #FEF3C7);" placeholder="https://example.com/landing-page" required>
                                <div class="text-xs text-gray-500 mt-1">The full website URL (landing page)</div>
                            </div>

                            <!-- Campaign Source -->
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Campaign Source *</label>
                                <div class="flex gap-2">
                                    <input type="text" id="utmSource" class="flex-1 px-3 py-2 border-2 rounded-lg focus:outline-none text-gray-800 text-sm" style="border-color: #FDE68A; background: linear-gradient(45deg, #FFFBEB, #FEF3C7);" placeholder="google, facebook, newsletter" required>
                                    <select id="sourcePresets" onchange="fillSource()" class="px-3 py-2 border-2 rounded-lg text-sm" style="border-color: #FDE68A;">
                                        <option value="">Presets</option>
                                        <option value="google">Google</option>
                                        <option value="facebook">Facebook</option>
                                        <option value="twitter">Twitter</option>
                                        <option value="linkedin">LinkedIn</option>
                                        <option value="instagram">Instagram</option>
                                        <option value="youtube">YouTube</option>
                                        <option value="newsletter">Newsletter</option>
                                        <option value="email">Email</option>
                                    </select>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">Identify the advertiser, site, publication, etc.</div>
                            </div>

                            <!-- Campaign Medium -->
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Campaign Medium *</label>
                                <div class="flex gap-2">
                                    <input type="text" id="utmMedium" class="flex-1 px-3 py-2 border-2 rounded-lg focus:outline-none text-gray-800 text-sm" style="border-color: #FDE68A; background: linear-gradient(45deg, #FFFBEB, #FEF3C7);" placeholder="cpc, banner, email" required>
                                    <select id="mediumPresets" onchange="fillMedium()" class="px-3 py-2 border-2 rounded-lg text-sm" style="border-color: #FDE68A;">
                                        <option value="">Presets</option>
                                        <option value="cpc">CPC</option>
                                        <option value="cpm">CPM</option>
                                        <option value="social">Social</option>
                                        <option value="email">Email</option>
                                        <option value="organic">Organic</option>
                                        <option value="referral">Referral</option>
                                        <option value="display">Display</option>
                                        <option value="banner">Banner</option>
                                    </select>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">Marketing medium: cpc, banner, email</div>
                            </div>

                            <!-- Campaign Name -->
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Campaign Name *</label>
                                <input type="text" id="utmCampaign" class="w-full px-3 py-2 border-2 rounded-lg focus:outline-none text-gray-800 text-sm" style="border-color: #FDE68A; background: linear-gradient(45deg, #FFFBEB, #FEF3C7);" placeholder="spring_sale, product_launch" required>
                                <div class="text-xs text-gray-500 mt-1">Product, promo code, or slogan</div>
                            </div>

                            <!-- Optional Parameters -->
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Optional Parameters</label>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-gray-600 font-medium mb-1 text-sm">Campaign Term:</label>
                                        <input type="text" id="utmTerm" class="w-full px-3 py-2 border-2 rounded-lg focus:outline-none text-gray-800 text-sm" style="border-color: #FDE68A; background: linear-gradient(45deg, #FFFBEB, #FEF3C7);" placeholder="running+shoes">
                                        <div class="text-xs text-gray-500 mt-1">Identify paid search keywords</div>
                                    </div>
                                    <div>
                                        <label class="block text-gray-600 font-medium mb-1 text-sm">Campaign Content:</label>
                                        <input type="text" id="utmContent" class="w-full px-3 py-2 border-2 rounded-lg focus:outline-none text-gray-800 text-sm" style="border-color: #FDE68A; background: linear-gradient(45deg, #FFFBEB, #FEF3C7);" placeholder="logolink, textlink">
                                        <div class="text-xs text-gray-500 mt-1">Differentiate ads or links</div>
                                    </div>
                                </div>
                            </div>

                            <button onclick="generateUTM()" class="w-full text-white font-bold py-3 lg:py-4 px-4 lg:px-6 rounded-lg lg:rounded-xl transform hover:scale-105 transition-all duration-300 shadow-lg text-sm lg:text-base" style="background: linear-gradient(45deg, #F59E0B, #D97706); box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);">
                                <svg class="w-4 h-4 lg:w-5 lg:h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Generate UTM URL
                            </button>
                        </div>

                        <!-- Output Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <!-- Generated URL -->
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <label class="block text-gray-700 font-bold text-base lg:text-lg">Generated UTM URL:</label>
                                    <button onclick="copyUTM()" id="copyBtn" class="text-white px-3 lg:px-4 py-1 lg:py-2 rounded-full text-xs lg:text-sm transition-all duration-300 transform hover:scale-105 shadow-lg" style="background: linear-gradient(45deg, #D97706, #F59E0B);">
                                        Copy URL
                                    </button>
                                </div>
                                <textarea id="generatedUTM" rows="6" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 resize-none font-mono text-sm lg:text-base" style="border-color: #FDE68A; background: linear-gradient(45deg, #FFFBEB, #FEF3C7);" readonly placeholder="Your UTM URL will appear here..."></textarea>
                            </div>

                            <!-- UTM Parameters Breakdown -->
                            <div id="utmBreakdown" class="hidden">
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #8B5CF6, #A855F7); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">UTM Parameters Breakdown</h3>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between p-2 bg-white rounded border">
                                        <span class="font-medium text-gray-600">utm_source:</span>
                                        <span id="breakdownSource" class="text-gray-800 font-mono"></span>
                                    </div>
                                    <div class="flex justify-between p-2 bg-white rounded border">
                                        <span class="font-medium text-gray-600">utm_medium:</span>
                                        <span id="breakdownMedium" class="text-gray-800 font-mono"></span>
                                    </div>
                                    <div class="flex justify-between p-2 bg-white rounded border">
                                        <span class="font-medium text-gray-600">utm_campaign:</span>
                                        <span id="breakdownCampaign" class="text-gray-800 font-mono"></span>
                                    </div>
                                    <div id="breakdownTermRow" class="flex justify-between p-2 bg-white rounded border hidden">
                                        <span class="font-medium text-gray-600">utm_term:</span>
                                        <span id="breakdownTerm" class="text-gray-800 font-mono"></span>
                                    </div>
                                    <div id="breakdownContentRow" class="flex justify-between p-2 bg-white rounded border hidden">
                                        <span class="font-medium text-gray-600">utm_content:</span>
                                        <span id="breakdownContent" class="text-gray-800 font-mono"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Campaign Preview -->
                            <div id="campaignPreview" class="hidden">
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #22C55E, #16A34A); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Campaign Preview</h3>
                                <div class="p-4 bg-white rounded-lg border shadow-sm">
                                    <div class="text-sm text-gray-600 mb-2">How this will appear in Google Analytics:</div>
                                    <div class="space-y-1 text-sm">
                                        <div><strong>Source/Medium:</strong> <span id="previewSourceMedium" class="text-blue-600"></span></div>
                                        <div><strong>Campaign:</strong> <span id="previewCampaign" class="text-green-600"></span></div>
                                        <div id="previewTermDiv" class="hidden"><strong>Keyword:</strong> <span id="previewTerm" class="text-purple-600"></span></div>
                                        <div id="previewContentDiv" class="hidden"><strong>Ad Content:</strong> <span id="previewContent" class="text-orange-600"></span></div>
                                    </div>
                                </div>
                            </div>

                            <!-- URL Validation -->
                            <div id="urlValidation" class="hidden">
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4 text-green-600">URL Validation</h3>
                                <div id="validationResults" class="space-y-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Campaign Templates -->
                    <div class="mt-8">
                        <h3 class="text-lg lg:text-xl font-bold mb-4 text-gray-800">Quick Campaign Templates</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                            <button onclick="loadTemplate('email')" class="p-3 text-left border-2 rounded-lg hover:bg-blue-50 transition-all duration-300" style="border-color: #3B82F6;">
                                <div class="font-bold text-blue-700 text-sm">Email Campaign</div>
                                <div class="text-xs text-gray-600">Newsletter/Email marketing</div>
                            </button>
                            <button onclick="loadTemplate('social')" class="p-3 text-left border-2 rounded-lg hover:bg-green-50 transition-all duration-300" style="border-color: #22C55E;">
                                <div class="font-bold text-green-700 text-sm">Social Media</div>
                                <div class="text-xs text-gray-600">Facebook, Twitter, LinkedIn</div>
                            </button>
                            <button onclick="loadTemplate('ppc')" class="p-3 text-left border-2 rounded-lg hover:bg-purple-50 transition-all duration-300" style="border-color: #8B5CF6;">
                                <div class="font-bold text-purple-700 text-sm">PPC Campaign</div>
                                <div class="text-xs text-gray-600">Google Ads, Bing Ads</div>
                            </button>
                            <button onclick="loadTemplate('display')" class="p-3 text-left border-2 rounded-lg hover:bg-orange-50 transition-all duration-300" style="border-color: #F97316;">
                                <div class="font-bold text-orange-700 text-sm">Display Ads</div>
                                <div class="text-xs text-gray-600">Banner advertising</div>
                            </button>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 lg:mt-8 text-center">
                        <div class="flex flex-wrap justify-center gap-2 lg:gap-4">
                            <button onclick="clearAll()" class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-300 text-sm lg:text-base">
                                Clear All
                            </button>
                            <button onclick="validateURL()" class="px-4 py-2 text-green-600 hover:text-green-800 border border-green-300 rounded-lg hover:bg-green-50 transition-all duration-300 text-sm lg:text-base">
                                Validate URL
                            </button>
                            <button onclick="shortenUTM()" class="px-4 py-2 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-lg hover:bg-blue-50 transition-all duration-300 text-sm lg:text-base">
                                Shorten URL
                            </button>
                            <button onclick="exportCSV()" class="px-4 py-2 text-purple-600 hover:text-purple-800 border border-purple-300 rounded-lg hover:bg-purple-50 transition-all duration-300 text-sm lg:text-base">
                                Export CSV
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>

    <script>
        let generatedUTMUrl = '';

        function fillSource() {
            const preset = document.getElementById('sourcePresets').value;
            if (preset) {
                document.getElementById('utmSource').value = preset;
                document.getElementById('sourcePresets').value = '';
            }
        }

        function fillMedium() {
            const preset = document.getElementById('mediumPresets').value;
            if (preset) {
                document.getElementById('utmMedium').value = preset;
                document.getElementById('mediumPresets').value = '';
            }
        }

        function generateUTM() {
            const websiteUrl = document.getElementById('websiteUrl').value.trim();
            const utmSource = document.getElementById('utmSource').value.trim();
            const utmMedium = document.getElementById('utmMedium').value.trim();
            const utmCampaign = document.getElementById('utmCampaign').value.trim();
            const utmTerm = document.getElementById('utmTerm').value.trim();
            const utmContent = document.getElementById('utmContent').value.trim();

            // Validate required fields
            if (!websiteUrl || !utmSource || !utmMedium || !utmCampaign) {
                alert('Please fill in all required fields (Website URL, Source, Medium, and Campaign Name).');
                return;
            }

            // Validate URL format
            try {
                new URL(websiteUrl);
            } catch (e) {
                alert('Please enter a valid website URL (including http:// or https://).');
                return;
            }

            // Build UTM URL
            const url = new URL(websiteUrl);
            url.searchParams.set('utm_source', utmSource);
            url.searchParams.set('utm_medium', utmMedium);
            url.searchParams.set('utm_campaign', utmCampaign);
            
            if (utmTerm) {
                url.searchParams.set('utm_term', utmTerm);
            }
            
            if (utmContent) {
                url.searchParams.set('utm_content', utmContent);
            }

            generatedUTMUrl = url.toString();
            document.getElementById('generatedUTM').value = generatedUTMUrl;

            // Update breakdown
            updateUTMBreakdown(utmSource, utmMedium, utmCampaign, utmTerm, utmContent);
            
            // Update preview
            updateCampaignPreview(utmSource, utmMedium, utmCampaign, utmTerm, utmContent);
            
            document.getElementById('utmBreakdown').classList.remove('hidden');
            document.getElementById('campaignPreview').classList.remove('hidden');
        }

        function updateUTMBreakdown(source, medium, campaign, term, content) {
            document.getElementById('breakdownSource').textContent = source;
            document.getElementById('breakdownMedium').textContent = medium;
            document.getElementById('breakdownCampaign').textContent = campaign;
            
            if (term) {
                document.getElementById('breakdownTerm').textContent = term;
                document.getElementById('breakdownTermRow').classList.remove('hidden');
            } else {
                document.getElementById('breakdownTermRow').classList.add('hidden');
            }
            
            if (content) {
                document.getElementById('breakdownContent').textContent = content;
                document.getElementById('breakdownContentRow').classList.remove('hidden');
            } else {
                document.getElementById('breakdownContentRow').classList.add('hidden');
            }
        }

        function updateCampaignPreview(source, medium, campaign, term, content) {
            document.getElementById('previewSourceMedium').textContent = `${source} / ${medium}`;
            document.getElementById('previewCampaign').textContent = campaign;
            
            if (term) {
                document.getElementById('previewTerm').textContent = term;
                document.getElementById('previewTermDiv').classList.remove('hidden');
            } else {
                document.getElementById('previewTermDiv').classList.add('hidden');
            }
            
            if (content) {
                document.getElementById('previewContent').textContent = content;
                document.getElementById('previewContentDiv').classList.remove('hidden');
            } else {
                document.getElementById('previewContentDiv').classList.add('hidden');
            }
        }

        function copyUTM() {
            if (!generatedUTMUrl) {
                alert('No UTM URL to copy. Please generate a URL first.');
                return;
            }
            
            navigator.clipboard.writeText(generatedUTMUrl).then(() => {
                const button = document.getElementById('copyBtn');
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            }).catch(() => {
                alert('UTM URL copied to clipboard!');
            });
        }

        function loadTemplate(type) {
            const templates = {
                email: {
                    source: 'newsletter',
                    medium: 'email',
                    campaign: 'monthly_newsletter',
                    content: 'header_link'
                },
                social: {
                    source: 'facebook',
                    medium: 'social',
                    campaign: 'spring_promotion',
                    content: 'post_link'
                },
                ppc: {
                    source: 'google',
                    medium: 'cpc',
                    campaign: 'brand_keywords',
                    term: 'running+shoes',
                    content: 'ad_group_1'
                },
                display: {
                    source: 'partner_site',
                    medium: 'display',
                    campaign: 'banner_campaign',
                    content: 'leaderboard_banner'
                }
            };

            const template = templates[type];
            if (template) {
                document.getElementById('utmSource').value = template.source;
                document.getElementById('utmMedium').value = template.medium;
                document.getElementById('utmCampaign').value = template.campaign;
                document.getElementById('utmTerm').value = template.term || '';
                document.getElementById('utmContent').value = template.content || '';
            }
        }

        function validateURL() {
            if (!generatedUTMUrl) {
                alert('Please generate a UTM URL first.');
                return;
            }

            const validationResults = [];
            
            try {
                const url = new URL(generatedUTMUrl);
                
                // Check required parameters
                if (url.searchParams.get('utm_source')) {
                    validationResults.push({ type: 'success', message: 'UTM Source is present' });
                } else {
                    validationResults.push({ type: 'error', message: 'UTM Source is missing' });
                }
                
                if (url.searchParams.get('utm_medium')) {
                    validationResults.push({ type: 'success', message: 'UTM Medium is present' });
                } else {
                    validationResults.push({ type: 'error', message: 'UTM Medium is missing' });
                }
                
                if (url.searchParams.get('utm_campaign')) {
                    validationResults.push({ type: 'success', message: 'UTM Campaign is present' });
                } else {
                    validationResults.push({ type: 'error', message: 'UTM Campaign is missing' });
                }
                
                // Check URL length
                if (generatedUTMUrl.length > 2000) {
                    validationResults.push({ type: 'warning', message: 'URL is very long (>2000 characters)' });
                } else {
                    validationResults.push({ type: 'success', message: 'URL length is acceptable' });
                }
                
                // Check for special characters
                const hasSpecialChars = /[^a-zA-Z0-9\-_.~:/?#[\]@!$&'()*+,;=%]/.test(generatedUTMUrl);
                if (hasSpecialChars) {
                    validationResults.push({ type: 'warning', message: 'URL contains special characters that may need encoding' });
                } else {
                    validationResults.push({ type: 'success', message: 'URL uses safe characters' });
                }
                
            } catch (e) {
                validationResults.push({ type: 'error', message: 'Invalid URL format' });
            }

            displayValidationResults(validationResults);
        }

        function displayValidationResults(results) {
            const container = document.getElementById('validationResults');
            container.innerHTML = '';

            results.forEach(result => {
                const div = document.createElement('div');
                div.className = `p-3 rounded border-l-4 text-sm ${getValidationClass(result.type)}`;
                div.textContent = result.message;
                container.appendChild(div);
            });

            document.getElementById('urlValidation').classList.remove('hidden');
        }

        function getValidationClass(type) {
            switch(type) {
                case 'success': return 'bg-green-50 border-green-400 text-green-700';
                case 'warning': return 'bg-yellow-50 border-yellow-400 text-yellow-700';
                case 'error': return 'bg-red-50 border-red-400 text-red-700';
                default: return 'bg-gray-50 border-gray-400 text-gray-700';
            }
        }

        function shortenUTM() {
            if (!generatedUTMUrl) {
                alert('Please generate a UTM URL first.');
                return;
            }
            alert('URL Shortening:\n\nYour UTM URL can be shortened using services like:\n• bit.ly\n• tinyurl.com\n• short.ly\n\nThis maintains all UTM parameters while creating a cleaner link for sharing.');
        }

        function exportCSV() {
            if (!generatedUTMUrl) {
                alert('Please generate a UTM URL first.');
                return;
            }

            const csvContent = `Campaign Name,Source,Medium,Term,Content,Full URL\n"${document.getElementById('utmCampaign').value}","${document.getElementById('utmSource').value}","${document.getElementById('utmMedium').value}","${document.getElementById('utmTerm').value}","${document.getElementById('utmContent').value}","${generatedUTMUrl}"`;
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'utm-campaign.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function clearAll() {
            document.getElementById('websiteUrl').value = '';
            document.getElementById('utmSource').value = '';
            document.getElementById('utmMedium').value = '';
            document.getElementById('utmCampaign').value = '';
            document.getElementById('utmTerm').value = '';
            document.getElementById('utmContent').value = '';
            document.getElementById('generatedUTM').value = '';
            document.getElementById('utmBreakdown').classList.add('hidden');
            document.getElementById('campaignPreview').classList.add('hidden');
            document.getElementById('urlValidation').classList.add('hidden');
            generatedUTMUrl = '';
        }

        // Auto-focus on website URL input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('websiteUrl').focus();
        });
    </script>
</body>
</html>
