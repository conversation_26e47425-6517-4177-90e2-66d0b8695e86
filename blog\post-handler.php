<?php
// This file handles dynamic post URLs
// Extract slug from URL path
$request_uri = $_SERVER['REQUEST_URI'];
$path_parts = explode('/', trim($request_uri, '/'));

// Find the slug (should be after 'blog')
$slug = '';
$blog_index = array_search('blog', $path_parts);
if ($blog_index !== false && isset($path_parts[$blog_index + 1])) {
    $slug = $path_parts[$blog_index + 1];
    // Remove .php extension if present
    $slug = str_replace('.php', '', $slug);
}

if ($slug) {
    // Include the blog post page with slug parameter
    $_GET['slug'] = $slug;
    include '../pages/blog-post.php';
} else {
    // Redirect to blog index
    header('Location: ../pages/blog.php');
    exit;
}
?>
