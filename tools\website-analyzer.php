<?php
require_once '../config/config.php';

$page_title = 'Website Analyzer - Free SEO Website Analysis Tool';
$page_description = 'Analyze any website for SEO performance, speed, security, and technical issues. Get comprehensive website audit reports instantly.';
$page_keywords = 'website analyzer, seo analyzer, website audit, site analysis, seo checker, website performance, technical seo, site audit';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo get_current_url(); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo get_current_url(); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Website Analyzer",
        "description": "Analyze any website for SEO performance, speed, security, and technical issues. Get comprehensive website audit reports instantly.",
        "url": "<?php echo get_current_url(); ?>",
        "applicationCategory": "SEO Tool",
        "operatingSystem": "Any",
        "permissions": "browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "provider": {
            "@type": "Organization",
            "name": "SEO Agency",
            "url": "http://localhost/test"
        }
    }
    </script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/tailwind.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="font-inter">
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <main class="pt-20 lg:pt-24">
        <!-- Hero Section -->
        <section class="py-12 lg:py-16 relative overflow-hidden" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 15%, #EF4444 30%, #F97316 45%, #EAB308 60%, #22C55E 75%, #06B6D4 90%, #3B82F6 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-6xl">
                <div class="text-center max-w-4xl mx-auto animate-on-scroll">
                    <h1 class="text-3xl lg:text-4xl font-bold text-white mb-8 lg:mb-10" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        Website Analyzer
                    </h1>
                    <p class="text-lg lg:text-xl text-white leading-relaxed mb-10 lg:mb-12" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Comprehensive website analysis for SEO, performance, security, and technical optimization.
                    </p>
                </div>
            </div>
        </section>

        <!-- Spacer -->
        <div class="py-6"></div>

        <!-- Tool Section -->
        <section class="py-8 lg:py-12 relative overflow-hidden" style="background: linear-gradient(135deg, #E0E7FF 0%, #F3E8FF 20%, #FCE7F3 40%, #FEF2F2 60%, #FFF7ED 80%, #F7FEE7 100%);">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl">
                <div class="rounded-xl shadow-lg p-6 lg:p-8 border-2" style="background: linear-gradient(135deg, #FFFFFF, #F8FAFC, #F1F5F9); border-color: #EF4444;">
                    <div class="text-center mb-6 lg:mb-8">
                        <h2 class="text-2xl lg:text-3xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #EF4444, #DC2626); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Comprehensive Website Audit Tool</h2>
                        <p class="text-gray-700 text-base lg:text-lg font-medium">Analyze website performance, SEO, security, and technical aspects for optimization insights</p>
                    </div>

                    <!-- Tool Interface -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                        <!-- Input Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Website URL:</label>
                                <input type="url" id="websiteUrl" class="w-full px-3 lg:px-4 py-2 lg:py-3 border-2 rounded-lg lg:rounded-xl focus:outline-none text-gray-800 font-medium text-sm lg:text-base" style="border-color: #FECACA; background: linear-gradient(45deg, #FEF2F2, #FECACA);" placeholder="https://example.com">
                            </div>

                            <!-- Analysis Options -->
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Analysis Categories:</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="analyzeSEO" checked class="mr-2 text-red-500">
                                        <span class="text-gray-700 font-medium text-sm">SEO Analysis</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="analyzePerformance" checked class="mr-2 text-red-500">
                                        <span class="text-gray-700 font-medium text-sm">Performance Analysis</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="analyzeSecurity" class="mr-2 text-red-500">
                                        <span class="text-gray-700 font-medium text-sm">Security Analysis</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="analyzeTechnical" checked class="mr-2 text-red-500">
                                        <span class="text-gray-700 font-medium text-sm">Technical Analysis</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Analysis Depth -->
                            <div>
                                <label class="block text-gray-700 font-bold mb-2 text-base lg:text-lg">Analysis Depth:</label>
                                <select id="analysisDepth" class="w-full px-3 py-2 border rounded text-sm" style="border-color: #FECACA;">
                                    <option value="basic">Basic Analysis</option>
                                    <option value="detailed" selected>Detailed Analysis</option>
                                    <option value="comprehensive">Comprehensive Analysis</option>
                                </select>
                            </div>

                            <button onclick="analyzeWebsite()" class="w-full text-white font-bold py-3 lg:py-4 px-4 lg:px-6 rounded-lg lg:rounded-xl transform hover:scale-105 transition-all duration-300 shadow-lg text-sm lg:text-base" style="background: linear-gradient(45deg, #EF4444, #DC2626); box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);">
                                <svg class="w-4 h-4 lg:w-5 lg:h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Analyze Website
                            </button>
                        </div>

                        <!-- Results Section -->
                        <div class="space-y-4 lg:space-y-5">
                            <!-- Overall Score -->
                            <div id="overallScore" class="hidden">
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4 text-gray-900">Overall Score</h3>
                                <div class="rounded-lg lg:rounded-xl p-4 lg:p-5 border-2 text-center" style="background: linear-gradient(135deg, #FEF2F2, #FECACA); border-color: #EF4444;">
                                    <div id="totalScore" class="text-4xl lg:text-5xl font-bold text-red-600 mb-2">0</div>
                                    <div class="text-sm lg:text-base text-gray-700 font-medium">out of 100</div>
                                    <div id="scoreGrade" class="text-xs lg:text-sm text-gray-600 mt-2">Grade: F</div>
                                </div>
                            </div>

                            <!-- Category Scores -->
                            <div id="categoryScores" class="hidden">
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4" style="background: linear-gradient(45deg, #8B5CF6, #A855F7); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: transparent;">Category Scores</h3>
                                <div id="scoreBreakdown" class="space-y-3">
                                    <!-- Category scores will be populated here -->
                                </div>
                            </div>

                            <!-- Issues Found -->
                            <div id="issuesFound" class="hidden">
                                <h3 class="text-lg lg:text-xl font-bold mb-3 lg:mb-4 text-gray-900">Issues Found</h3>
                                <div class="rounded-lg lg:rounded-xl p-3 lg:p-4 border-2 max-h-[200px] overflow-y-auto" style="background: linear-gradient(135deg, #FEF2F2, #FECACA); border-color: #EF4444;">
                                    <div id="issuesList" class="space-y-2 text-sm">
                                        <!-- Issues will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 lg:mt-8 text-center">
                        <div class="flex flex-wrap justify-center gap-2 lg:gap-4">
                            <button onclick="clearAll()" class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-300 text-sm lg:text-base">
                                Clear All
                            </button>
                            <button onclick="loadSample()" class="px-4 py-2 text-red-600 hover:text-red-800 border border-red-300 rounded-lg hover:bg-red-50 transition-all duration-300 text-sm lg:text-base">
                                Load Sample
                            </button>
                            <button onclick="exportReport()" class="px-4 py-2 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-lg hover:bg-blue-50 transition-all duration-300 text-sm lg:text-base">
                                Export Report
                            </button>
                            <button onclick="generateRecommendations()" class="px-4 py-2 text-green-600 hover:text-green-800 border border-green-300 rounded-lg hover:bg-green-50 transition-all duration-300 text-sm lg:text-base">
                                Get Recommendations
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>

    <script>
        let analysisResults = {};

        function analyzeWebsite() {
            const websiteUrl = document.getElementById('websiteUrl').value.trim();
            const analyzeSEO = document.getElementById('analyzeSEO').checked;
            const analyzePerformance = document.getElementById('analyzePerformance').checked;
            const analyzeSecurity = document.getElementById('analyzeSecurity').checked;
            const analyzeTechnical = document.getElementById('analyzeTechnical').checked;
            const analysisDepth = document.getElementById('analysisDepth').value;

            if (!websiteUrl) {
                alert('Please enter a website URL to analyze.');
                return;
            }

            if (!isValidURL(websiteUrl)) {
                alert('Please enter a valid URL (e.g., https://example.com).');
                return;
            }

            // Simulate website analysis
            analysisResults = performWebsiteAnalysis(websiteUrl, {
                analyzeSEO,
                analyzePerformance,
                analyzeSecurity,
                analyzeTechnical,
                analysisDepth
            });

            // Display results
            displayOverallScore(analysisResults);
            displayCategoryScores(analysisResults);
            displayIssues(analysisResults);
        }

        function performWebsiteAnalysis(url, options) {
            // Simulate analysis results
            const results = {
                url: url,
                categories: {},
                issues: [],
                recommendations: []
            };

            let totalScore = 0;
            let categoryCount = 0;

            // SEO Analysis
            if (options.analyzeSEO) {
                const seoScore = Math.floor(Math.random() * 40) + 60; // 60-100
                results.categories.seo = {
                    name: 'SEO',
                    score: seoScore,
                    status: getScoreStatus(seoScore)
                };
                totalScore += seoScore;
                categoryCount++;

                if (seoScore < 80) {
                    results.issues.push({
                        category: 'SEO',
                        severity: 'medium',
                        issue: 'Meta description missing or too short',
                        recommendation: 'Add descriptive meta descriptions (150-160 characters)'
                    });
                }
            }

            // Performance Analysis
            if (options.analyzePerformance) {
                const perfScore = Math.floor(Math.random() * 50) + 50; // 50-100
                results.categories.performance = {
                    name: 'Performance',
                    score: perfScore,
                    status: getScoreStatus(perfScore)
                };
                totalScore += perfScore;
                categoryCount++;

                if (perfScore < 70) {
                    results.issues.push({
                        category: 'Performance',
                        severity: 'high',
                        issue: 'Large image files detected',
                        recommendation: 'Optimize images and use modern formats (WebP, AVIF)'
                    });
                }
            }

            // Security Analysis
            if (options.analyzeSecurity) {
                const secScore = Math.floor(Math.random() * 30) + 70; // 70-100
                results.categories.security = {
                    name: 'Security',
                    score: secScore,
                    status: getScoreStatus(secScore)
                };
                totalScore += secScore;
                categoryCount++;

                if (secScore < 85) {
                    results.issues.push({
                        category: 'Security',
                        severity: 'high',
                        issue: 'Missing security headers',
                        recommendation: 'Implement HTTPS, CSP, and security headers'
                    });
                }
            }

            // Technical Analysis
            if (options.analyzeTechnical) {
                const techScore = Math.floor(Math.random() * 35) + 65; // 65-100
                results.categories.technical = {
                    name: 'Technical',
                    score: techScore,
                    status: getScoreStatus(techScore)
                };
                totalScore += techScore;
                categoryCount++;

                if (techScore < 75) {
                    results.issues.push({
                        category: 'Technical',
                        severity: 'medium',
                        issue: 'Missing structured data markup',
                        recommendation: 'Add JSON-LD structured data for better search visibility'
                    });
                }
            }

            results.overallScore = categoryCount > 0 ? Math.round(totalScore / categoryCount) : 0;
            results.grade = getScoreGrade(results.overallScore);

            return results;
        }

        function getScoreStatus(score) {
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            if (score >= 70) return 'fair';
            if (score >= 60) return 'poor';
            return 'critical';
        }

        function getScoreGrade(score) {
            if (score >= 90) return 'A+';
            if (score >= 85) return 'A';
            if (score >= 80) return 'B+';
            if (score >= 75) return 'B';
            if (score >= 70) return 'C+';
            if (score >= 65) return 'C';
            if (score >= 60) return 'D';
            return 'F';
        }

        function displayOverallScore(results) {
            document.getElementById('totalScore').textContent = results.overallScore;
            document.getElementById('scoreGrade').textContent = `Grade: ${results.grade}`;
            document.getElementById('overallScore').classList.remove('hidden');
        }

        function displayCategoryScores(results) {
            const scoreBreakdown = document.getElementById('scoreBreakdown');
            scoreBreakdown.innerHTML = '';

            Object.values(results.categories).forEach(category => {
                const div = document.createElement('div');
                const statusColors = {
                    'excellent': 'text-green-700 bg-green-50 border-green-200',
                    'good': 'text-blue-700 bg-blue-50 border-blue-200',
                    'fair': 'text-yellow-700 bg-yellow-50 border-yellow-200',
                    'poor': 'text-orange-700 bg-orange-50 border-orange-200',
                    'critical': 'text-red-700 bg-red-50 border-red-200'
                };

                div.className = `p-3 rounded border ${statusColors[category.status]}`;
                div.innerHTML = `
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">${category.name}</span>
                        <span class="font-bold">${category.score}/100</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div class="h-2 rounded-full transition-all duration-500" style="width: ${category.score}%; background: ${getScoreColor(category.score)};"></div>
                    </div>
                `;
                scoreBreakdown.appendChild(div);
            });

            document.getElementById('categoryScores').classList.remove('hidden');
        }

        function getScoreColor(score) {
            if (score >= 90) return '#10B981';
            if (score >= 80) return '#3B82F6';
            if (score >= 70) return '#F59E0B';
            if (score >= 60) return '#F97316';
            return '#EF4444';
        }

        function displayIssues(results) {
            const issuesList = document.getElementById('issuesList');
            issuesList.innerHTML = '';

            if (results.issues.length === 0) {
                issuesList.innerHTML = '<div class="text-green-600 font-medium">No critical issues found!</div>';
            } else {
                results.issues.forEach(issue => {
                    const div = document.createElement('div');
                    const severityColors = {
                        'high': 'text-red-600',
                        'medium': 'text-orange-600',
                        'low': 'text-yellow-600'
                    };

                    div.className = 'border-l-4 border-red-400 pl-3';
                    div.innerHTML = `
                        <div class="font-semibold ${severityColors[issue.severity]}">[${issue.category}] ${issue.issue}</div>
                        <div class="text-gray-600 text-xs mt-1">${issue.recommendation}</div>
                    `;
                    issuesList.appendChild(div);
                });
            }

            document.getElementById('issuesFound').classList.remove('hidden');
        }

        function isValidURL(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }

        function exportReport() {
            if (!analysisResults.overallScore) {
                alert('No analysis results to export. Please analyze a website first.');
                return;
            }

            let report = `Website Analysis Report\n`;
            report += `URL: ${analysisResults.url}\n`;
            report += `Generated: ${new Date().toLocaleString()}\n\n`;
            report += `Overall Score: ${analysisResults.overallScore}/100 (Grade: ${analysisResults.grade})\n\n`;

            report += `Category Breakdown:\n`;
            Object.values(analysisResults.categories).forEach(category => {
                report += `${category.name}: ${category.score}/100 (${category.status})\n`;
            });

            report += `\nIssues Found:\n`;
            analysisResults.issues.forEach((issue, index) => {
                report += `${index + 1}. [${issue.category}] ${issue.issue}\n`;
                report += `   Recommendation: ${issue.recommendation}\n\n`;
            });

            const blob = new Blob([report], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'website-analysis-report.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function generateRecommendations() {
            if (!analysisResults.overallScore) {
                alert('No analysis results available. Please analyze a website first.');
                return;
            }

            let recommendations = 'Website Optimization Recommendations:\n\n';
            
            if (analysisResults.overallScore < 80) {
                recommendations += '1. Improve overall website performance and SEO\n';
                recommendations += '2. Optimize images and reduce file sizes\n';
                recommendations += '3. Implement proper meta tags and structured data\n';
                recommendations += '4. Enhance website security with HTTPS and headers\n';
                recommendations += '5. Improve page loading speed and user experience\n\n';
            }

            recommendations += 'Specific Issues to Address:\n';
            analysisResults.issues.forEach((issue, index) => {
                recommendations += `${index + 1}. ${issue.recommendation}\n`;
            });

            alert(recommendations);
        }

        function clearAll() {
            document.getElementById('websiteUrl').value = '';
            document.getElementById('analyzeSEO').checked = true;
            document.getElementById('analyzePerformance').checked = true;
            document.getElementById('analyzeSecurity').checked = false;
            document.getElementById('analyzeTechnical').checked = true;
            document.getElementById('analysisDepth').value = 'detailed';
            document.getElementById('overallScore').classList.add('hidden');
            document.getElementById('categoryScores').classList.add('hidden');
            document.getElementById('issuesFound').classList.add('hidden');
            analysisResults = {};
        }

        function loadSample() {
            document.getElementById('websiteUrl').value = 'https://example.com';
        }
    </script>
</body>
</html>
