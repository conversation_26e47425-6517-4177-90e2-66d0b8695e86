# Create ZIP file on Desktop
$desktopPath = [Environment]::GetFolderPath("Desktop")
$zipPath = Join-Path $desktopPath "loganixseo.com.zip"
$sourceDir = Get-Location

Write-Host "Creating ZIP file on Desktop..." -ForegroundColor Green
Write-Host "Source: $sourceDir" -ForegroundColor Yellow
Write-Host "ZIP file: $zipPath" -ForegroundColor Yellow

# Remove existing ZIP if it exists
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
    Write-Host "Removed existing ZIP file" -ForegroundColor Yellow
}

# Create ZIP file
try {
    Compress-Archive -Path "$sourceDir\*" -DestinationPath $zipPath -Force
    Write-Host ""
    Write-Host "✅ ZIP file created successfully!" -ForegroundColor Green
    Write-Host "Location: $zipPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Instructions:" -ForegroundColor Yellow
    Write-Host "1. Extract the ZIP file on your desktop" -ForegroundColor White
    Write-Host "2. Upload the extracted folder to your live hosting" -ForegroundColor White
    Write-Host "3. Run fix-wordpress-urls.php after upload" -ForegroundColor White
    Write-Host "4. Update WordPress permalinks in admin" -ForegroundColor White
    
    $zipSize = [math]::Round((Get-Item $zipPath).Length / 1MB, 2)
    Write-Host ""
    Write-Host "ZIP file size: $zipSize MB" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error creating ZIP: $($_.Exception.Message)" -ForegroundColor Red
}
