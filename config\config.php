<?php
// Configuration file for SEO Agency Website

// Site Configuration
define('SITE_NAME', 'LoganixSEO');
define('SITE_DESCRIPTION', 'Professional SEO services and guest posting solutions for growing businesses. We help you achieve higher rankings and drive organic traffic through proven strategies.');
define('SITE_URL', 'https://loganixseo.com');
define('BASE_URL', 'https://loganixseo.com');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+97-1503306703');

// SEO Meta Tags
define('DEFAULT_TITLE', 'LoganixSEO - Boost Your Rankings with Real Guest Posts');
define('DEFAULT_DESCRIPTION', 'Get high-authority backlinks from real websites with genuine traffic. Our white-hat SEO strategies deliver measurable results that boost your search rankings. Trusted by 500+ businesses worldwide.');
define('DEFAULT_KEYWORDS', 'guest posting, link building, SEO services, backlinks, high authority websites, white-hat SEO, search engine optimization, blogger outreach, content writing, niche relevant, do-follow links, SEO agency, organic traffic, search rankings');

// Social Media
define('FACEBOOK_URL', 'https://www.facebook.com/profile.php?id=61570061610922');
define('TWITTER_URL', 'https://x.com/');
define('LINKEDIN_URL', 'https://www.linkedin.com/in/isaiah-abr-*********/');
define('INSTAGRAM_URL', '#');
define('WHATSAPP_URL', 'https://wa.me/971503306703');

// Contact Information
define('COMPANY_ADDRESS', 'UAE, Ajman');
define('BUSINESS_HOURS', '24/7 Support Available');
define('COMPANY_TAGLINE', 'Professional SEO & Link Building Services');

// Database Configuration (if needed in future)
define('DB_HOST', 'localhost');
define('DB_NAME', 'seo_agency');
define('DB_USER', 'root');
define('DB_PASS', '');

// Error Reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('America/New_York');

// Environment Detection
$http_host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
$is_local = (strpos($http_host, 'localhost') !== false || strpos($http_host, '127.0.0.1') !== false);

// WordPress API Configuration
if ($is_local) {
    define('WP_API_BASE', 'http://localhost/loganixseo.com/wp/wp-json/wp/v2');
    define('WP_ADMIN_URL', 'http://localhost/loganixseo.com/wp-admin/');
    define('WP_BASE_URL', 'http://localhost/loganixseo.com/wp/');
} else {
    // Live server configuration - Dynamic URL detection
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    define('WP_API_BASE', $protocol . '://' . $host . '/wp/wp-json/wp/v2');
    define('WP_ADMIN_URL', $protocol . '://' . $host . '/wp-admin/');
    define('WP_BASE_URL', $protocol . '://' . $host . '/wp/');
}

// Helper Functions
function get_page_title($page_title = '') {
    if (!empty($page_title)) {
        return $page_title . ' - ' . SITE_NAME;
    }
    return DEFAULT_TITLE;
}

function get_page_description($page_description = '') {
    if (!empty($page_description)) {
        return $page_description;
    }
    return DEFAULT_DESCRIPTION;
}

function get_current_url() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
}

function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Helper function to get base path for navigation
function get_base_path() {
    $current_dir = dirname($_SERVER['SCRIPT_NAME']);

    // Check if we're on localhost
    $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);

    if ($is_localhost) {
        $base_dir = '/loganixseo.com'; // Localhost project directory

        // If we're in a subdirectory, calculate relative path to base
        if (strpos($current_dir, $base_dir) !== false) {
            $relative_depth = substr_count(str_replace($base_dir, '', $current_dir), '/');
            return str_repeat('../', $relative_depth);
        }
        return './';
    } else {
        // Live server - use absolute paths for reliability
        return '/';
    }
}

// Helper function to get absolute base URL
function get_absolute_base_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];

    // Check if we're on localhost
    $is_localhost = (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false);

    if ($is_localhost) {
        return $protocol . '://' . $host . '/loganixseo.com/';
    } else {
        return $protocol . '://' . $host . '/';
    }
}

// Helper function to get navigation links
function get_nav_link($page) {
    $base_url = get_absolute_base_url();

    $links = [
        'home' => $base_url . 'index.php',
        'about' => $base_url . 'pages/about.php',
        'blog' => $base_url . 'pages/blog.php',
        'tools' => $base_url . 'pages/tools.php',
        'write-for-us' => $base_url . 'pages/write-for-us.php',
        'contact' => $base_url . 'pages/contact.php'
    ];

    return isset($links[$page]) ? $links[$page] : '#';
}

// Include additional functions if needed
// require_once 'functions.php';
?>
