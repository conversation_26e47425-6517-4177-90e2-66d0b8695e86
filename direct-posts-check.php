<?php
/**
 * Direct Database Posts Check
 */

echo "<h1>Direct Database Posts Check</h1>";

// Database credentials
$db_name = 'jobzcsdn_login';
$db_user = 'jobzcsdn_login';
$db_pass = '47G28{lIlsf-';
$db_host = 'localhost';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    echo "<p style='color: green;'>✅ Database connected</p>";
    
    // Check posts
    $stmt = $pdo->prepare("SELECT ID, post_title, post_status, post_type FROM wp_posts WHERE post_type = 'post' ORDER BY ID DESC LIMIT 10");
    $stmt->execute();
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Posts in Database:</h2>";
    if (empty($posts)) {
        echo "<p style='color: red;'>❌ No posts found in database!</p>";
        echo "<p>Database imported successfully but no posts exist.</p>";
        
        // Check if any data exists
        $count_stmt = $pdo->prepare("SELECT COUNT(*) FROM wp_posts");
        $count_stmt->execute();
        $total_posts = $count_stmt->fetchColumn();
        echo "<p>Total wp_posts records: $total_posts</p>";
        
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Status</th><th>Type</th></tr>";
        foreach ($posts as $post) {
            echo "<tr>";
            echo "<td>{$post['ID']}</td>";
            echo "<td>{$post['post_title']}</td>";
            echo "<td>{$post['post_status']}</td>";
            echo "<td>{$post['post_type']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Count published posts
        $pub_stmt = $pdo->prepare("SELECT COUNT(*) FROM wp_posts WHERE post_status = 'publish' AND post_type = 'post'");
        $pub_stmt->execute();
        $published_count = $pub_stmt->fetchColumn();
        echo "<p><strong>Published posts: $published_count</strong></p>";
    }
    
    // Check WordPress URLs
    echo "<h2>WordPress URLs:</h2>";
    $url_stmt = $pdo->prepare("SELECT option_name, option_value FROM wp_options WHERE option_name IN ('siteurl', 'home')");
    $url_stmt->execute();
    $urls = $url_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($urls as $url) {
        echo "<p><strong>{$url['option_name']}:</strong> {$url['option_value']}</p>";
    }
    
    // Check permalink structure
    $permalink_stmt = $pdo->prepare("SELECT option_value FROM wp_options WHERE option_name = 'permalink_structure'");
    $permalink_stmt->execute();
    $permalink = $permalink_stmt->fetchColumn();
    echo "<p><strong>Permalink structure:</strong> " . ($permalink ?: 'Default (not set)') . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Next Steps:</h2>";
echo "<ul>";
echo "<li>If no posts found: Create posts in WordPress admin</li>";
echo "<li>If posts exist but not showing: Fix permalink structure</li>";
echo "<li>If URLs wrong: Run URL fix again</li>";
echo "</ul>";
?>

<style>
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
