<?php
require_once '../config/config.php';

$page_title = 'Blog';
$page_description = 'Stay updated with the latest SEO trends, link building strategies, and digital marketing insights from our expert team.';

// Simple hardcoded posts that will work immediately
function getSimplePosts() {
    return [
        [
            'id' => 1,
            'title' => 'Advanced SEO Strategies for 2025',
            'excerpt' => 'Discover the latest SEO techniques and strategies that will help your website rank higher in search engines this year.',
            'content' => 'Learn about advanced SEO strategies including technical SEO, content optimization, and link building techniques that will dominate 2025.',
            'date' => '2025-01-20 10:00:00',
            'author' => 'LoganixSEO Team',
            'slug' => 'advanced-seo-strategies-2025'
        ],
        [
            'id' => 2,
            'title' => 'Complete Link Building Guide for Beginners',
            'excerpt' => 'A comprehensive guide to link building that covers everything from basics to advanced techniques.',
            'content' => 'Link building is one of the most important aspects of SEO. Learn how to build high-quality backlinks that will boost your rankings.',
            'date' => '2025-01-19 09:30:00',
            'author' => 'LoganixSEO Team',
            'slug' => 'link-building-guide-beginners'
        ],
        [
            'id' => 3,
            'title' => 'Content Optimization Best Practices',
            'excerpt' => 'Learn how to optimize your content for both search engines and users with these proven techniques.',
            'content' => 'Content optimization is crucial for SEO success. Discover the best practices for creating SEO-friendly content that ranks.',
            'date' => '2025-01-18 14:15:00',
            'author' => 'LoganixSEO Team',
            'slug' => 'content-optimization-best-practices'
        ],
        [
            'id' => 4,
            'title' => 'Technical SEO Checklist 2025',
            'excerpt' => 'A complete technical SEO checklist to ensure your website is optimized for search engines.',
            'content' => 'Technical SEO forms the foundation of your SEO strategy. Use this comprehensive checklist to audit your website.',
            'date' => '2025-01-17 11:45:00',
            'author' => 'LoganixSEO Team',
            'slug' => 'technical-seo-checklist-2025'
        ],
        [
            'id' => 5,
            'title' => 'Local SEO Tips for Small Businesses',
            'excerpt' => 'Boost your local search rankings with these effective local SEO strategies for small businesses.',
            'content' => 'Local SEO is essential for small businesses. Learn how to optimize your business for local search and attract more customers.',
            'date' => '2025-01-16 16:20:00',
            'author' => 'LoganixSEO Team',
            'slug' => 'local-seo-tips-small-businesses'
        ]
    ];
}

$posts = getSimplePosts();
$featured_post = $posts[0] ?? null;

include '../includes/header.php';
?>

<main class="main-content">
    <div class="container">
        <!-- Hero Section -->
        <section class="blog-hero">
            <div class="hero-content">
                <h1 class="hero-title">Latest SEO Insights</h1>
                <p class="hero-description">Stay updated with cutting-edge SEO strategies, link building techniques, and digital marketing trends.</p>
            </div>
        </section>

        <!-- Featured Article -->
        <?php if ($featured_post): ?>
        <section class="featured-article">
            <h2 class="section-title">Featured Article</h2>
            <article class="featured-post">
                <div class="post-content">
                    <h3 class="post-title">
                        <a href="blog-post.php?id=<?= $featured_post['id'] ?>&slug=<?= $featured_post['slug'] ?>">
                            <?= htmlspecialchars($featured_post['title']) ?>
                        </a>
                    </h3>
                    <div class="post-meta">
                        <span class="post-date"><?= date('F j, Y', strtotime($featured_post['date'])) ?></span>
                        <span class="post-author">by <?= htmlspecialchars($featured_post['author']) ?></span>
                    </div>
                    <p class="post-excerpt"><?= htmlspecialchars($featured_post['excerpt']) ?></p>
                    <a href="blog-post.php?id=<?= $featured_post['id'] ?>&slug=<?= $featured_post['slug'] ?>" class="read-more-btn">Read Full Article</a>
                </div>
            </article>
        </section>
        <?php endif; ?>

        <!-- Recent Articles -->
        <section class="recent-articles">
            <h2 class="section-title">Recent SEO Articles</h2>
            <p class="section-description">Discover actionable insights and proven strategies to boost your search rankings.</p>
            
            <div class="posts-grid">
                <?php foreach ($posts as $post): ?>
                <article class="post-card">
                    <div class="post-content">
                        <h3 class="post-title">
                            <a href="blog-post.php?id=<?= $post['id'] ?>&slug=<?= $post['slug'] ?>">
                                <?= htmlspecialchars($post['title']) ?>
                            </a>
                        </h3>
                        <div class="post-meta">
                            <span class="post-date"><?= date('F j, Y', strtotime($post['date'])) ?></span>
                            <span class="post-author">by <?= htmlspecialchars($post['author']) ?></span>
                        </div>
                        <p class="post-excerpt"><?= htmlspecialchars($post['excerpt']) ?></p>
                        <a href="blog-post.php?id=<?= $post['id'] ?>&slug=<?= $post['slug'] ?>" class="read-more">Read More →</a>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- Newsletter Signup -->
        <section class="newsletter-section">
            <div class="newsletter-content">
                <h2>Stay Updated with SEO Trends</h2>
                <p>Get the latest SEO insights, link building strategies, and digital marketing tips delivered to your inbox.</p>
                <form class="newsletter-form" action="../includes/contact-handler.php" method="POST">
                    <input type="hidden" name="form_type" value="newsletter">
                    <div class="form-group">
                        <input type="email" name="email" placeholder="Enter your email address" required>
                        <button type="submit" class="subscribe-btn">Subscribe Now</button>
                    </div>
                    <p class="newsletter-note">Join 10,000+ SEO professionals. Unsubscribe anytime.</p>
                </form>
            </div>
        </section>
    </div>
</main>

<?php include '../includes/footer.php'; ?>

<style>
.blog-hero {
    text-align: center;
    padding: 60px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 60px;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.hero-description {
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.9;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #333;
    font-weight: 700;
}

.section-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 40px;
}

.featured-article {
    margin-bottom: 80px;
}

.featured-post {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}

.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.post-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #e1e5e9;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.post-title a {
    color: #333;
    text-decoration: none;
    font-size: 1.4rem;
    font-weight: 600;
    line-height: 1.4;
}

.post-title a:hover {
    color: #667eea;
}

.post-meta {
    margin: 15px 0;
    color: #888;
    font-size: 0.9rem;
}

.post-excerpt {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.read-more, .read-more-btn {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.read-more:hover, .read-more-btn:hover {
    color: #764ba2;
}

.read-more-btn {
    background: #667eea;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    display: inline-block;
    margin-top: 15px;
}

.newsletter-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 40px;
    border-radius: 12px;
    text-align: center;
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-group {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-group input {
    flex: 1;
    padding: 15px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
}

.subscribe-btn {
    background: white;
    color: #667eea;
    border: none;
    padding: 15px 30px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.subscribe-btn:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.newsletter-note {
    font-size: 0.9rem;
    opacity: 0.8;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .posts-grid {
        grid-template-columns: 1fr;
    }
    
    .form-group {
        flex-direction: column;
    }
    
    .featured-post, .post-card {
        padding: 20px;
    }
}
</style>
