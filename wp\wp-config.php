<?php
/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the website, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'jobzcsdn_login' );

/** Database username */
define( 'DB_USER', 'jobzcsdn_login' );

/** Database password */
define( 'DB_PASSWORD', '47G28{lIlsf-' );

/** Database hostname */
define( 'DB_HOST', 'localhost' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8mb4' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',         'FhOe?pA`HR&QF4k,U.&E|4?$f^H1v4K,)bu1VuJX1qmw4%kO_lFw:N|=buH,Vf}a' );
define( 'SECURE_AUTH_KEY',  'ZDZ+/Nqigl{E-+QOR`?vj cT4j k.V{QW*~?OSB(Vq^^@82%vM9V0~/Gz?s$ebs3' );
define( 'LOGGED_IN_KEY',    'M33Q@Nih}(shL#v&* 6?K!j)CL~<a[zfi:a|Rn@ud?D#6~-*|BA)$>[/mSU=dB+f' );
define( 'NONCE_KEY',        '^n]%l|5`QC{,VW)OXe~mef0l4 %l*>/02Rs!3 vY*}T8?]u^H4Tsp:E;S~aH[zfi' );
define( 'AUTH_SALT',        '&Nt}vNyON20;6;0XVeS{{X.:)rs{p37`7~1xU:+#((8Q5$b`wd>h%^bQx(F-B3<;' );
define( 'SECURE_AUTH_SALT', 'X[O}o3k<SFY^I$$eoW$%z[FJM@Bk;H~dh4$IiS?irB$xt1/IWO&e! QcIvF+N$3M' );
define( 'LOGGED_IN_SALT',   'uc2^6ES4?sQa7Xoit?ue`qt}L>uwW3WGUAVs#unLUI1^3OFne,ZAtU:p2;Z-b9GF' );
define( 'NONCE_SALT',       '?0cBPmP32q(WaQf&cPHDMxv7N60iUs*+w]jHzOWwSXg/*2{JX!Q0w^B9;kZ&&en[' );

/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */
$table_prefix = 'wp_';

/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/
 */
define( 'WP_DEBUG', false );

/* Add any custom values between this line and the "stop editing" line. */

/* That's all, stop editing! Happy publishing. */
// Auto-detect environment and set URLs accordingly
$http_host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
$is_local = (strpos($http_host, 'localhost') !== false || strpos($http_host, '127.0.0.1') !== false);
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';

if ($is_local) {
    define('WP_HOME', 'http://localhost/loganixseo.com/wp');
    define('WP_SITEURL', 'http://localhost/loganixseo.com/wp');
} else {
    define('WP_HOME', $protocol . '://' . $http_host . '/wp');
    define('WP_SITEURL', $protocol . '://' . $http_host . '/wp');
}


/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';
