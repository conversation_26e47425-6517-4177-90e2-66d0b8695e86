# Create LoganixSEO project copy on Desktop
$desktopPath = [Environment]::GetFolderPath("Desktop")
$zipPath = Join-Path $desktopPath "loganixseo-ready-for-hosting.zip"
$sourceDir = Get-Location

Write-Host "🚀 Creating LoganixSEO project for hosting..." -ForegroundColor Green
Write-Host "Source: $sourceDir" -ForegroundColor Yellow
Write-Host "ZIP file: $zipPath" -ForegroundColor Yellow

# Create ZIP file with timestamp to avoid conflicts
$timestamp = Get-Date -Format "yyyy-MM-dd-HHmm"
$zipPath = Join-Path $desktopPath "loganixseo-ready-for-hosting-$timestamp.zip"

try {
    Compress-Archive -Path "$sourceDir\*" -DestinationPath $zipPath -Force
    Write-Host ""
    Write-Host "✅ Project packaged successfully!" -ForegroundColor Green
    Write-Host "📁 Location: $zipPath" -ForegroundColor Cyan
    
    $zipSize = [math]::Round((Get-Item $zipPath).Length / 1MB, 2)
    Write-Host "📦 ZIP file size: $zipSize MB" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "🎯 DEPLOYMENT INSTRUCTIONS:" -ForegroundColor Yellow
    Write-Host "1. Extract this ZIP file" -ForegroundColor White
    Write-Host "2. Upload extracted folder to your hosting root directory" -ForegroundColor White
    Write-Host "3. Update database credentials in wp/wp-config.php (if needed)" -ForegroundColor White
    Write-Host "4. Run: https://yourdomain.com/fix-wordpress-urls.php" -ForegroundColor White
    Write-Host "5. Login to WordPress admin and update permalinks" -ForegroundColor White
    Write-Host "6. Test: https://yourdomain.com/pages/blog.php" -ForegroundColor White
    Write-Host "7. Delete fix-wordpress-urls.php for security" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 All WordPress URL issues have been fixed!" -ForegroundColor Green
    Write-Host "📝 Your posts will now fetch properly on live hosting" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error creating package: $($_.Exception.Message)" -ForegroundColor Red
}
