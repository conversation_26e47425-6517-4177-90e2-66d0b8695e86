<?php
require_once '../config/config.php';

$page_title = 'Blog - LoganixSEO';
$page_description = 'Stay updated with the latest SEO trends, link building strategies, and digital marketing insights from our expert team.';

// Working posts array
$posts = [
    [
        'id' => 1,
        'title' => 'Advanced SEO Strategies for 2025',
        'excerpt' => 'Discover the latest SEO techniques and strategies that will help your website rank higher in search engines this year.',
        'date' => '2025-01-20',
        'author' => 'LoganixSEO Team',
        'slug' => 'advanced-seo-strategies-2025'
    ],
    [
        'id' => 2,
        'title' => 'Complete Link Building Guide for Beginners',
        'excerpt' => 'A comprehensive guide to link building that covers everything from basics to advanced techniques.',
        'date' => '2025-01-19',
        'author' => 'LoganixSEO Team',
        'slug' => 'link-building-guide-beginners'
    ],
    [
        'id' => 3,
        'title' => 'Content Optimization Best Practices',
        'excerpt' => 'Learn how to optimize your content for both search engines and users with these proven techniques.',
        'date' => '2025-01-18',
        'author' => 'LoganixSEO Team',
        'slug' => 'content-optimization-best-practices'
    ],
    [
        'id' => 4,
        'title' => 'Technical SEO Checklist 2025',
        'excerpt' => 'A complete technical SEO checklist to ensure your website is optimized for search engines.',
        'date' => '2025-01-17',
        'author' => 'LoganixSEO Team',
        'slug' => 'technical-seo-checklist-2025'
    ],
    [
        'id' => 5,
        'title' => 'Local SEO Tips for Small Businesses',
        'excerpt' => 'Boost your local search rankings with these effective local SEO strategies for small businesses.',
        'date' => '2025-01-16',
        'author' => 'LoganixSEO Team',
        'slug' => 'local-seo-tips-small-businesses'
    ]
];

$featured_post = $posts[0];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    <meta name="description" content="<?= htmlspecialchars($page_description) ?>">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .blog-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .blog-header { text-align: center; margin-bottom: 50px; }
        .blog-title { font-size: 3rem; color: #333; margin-bottom: 20px; }
        .blog-description { font-size: 1.2rem; color: #666; }
        .featured-section { margin-bottom: 60px; }
        .section-title { font-size: 2rem; color: #333; margin-bottom: 30px; }
        .featured-post { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .post-title { font-size: 1.8rem; color: #333; margin-bottom: 15px; }
        .post-meta { color: #888; margin-bottom: 15px; }
        .post-excerpt { color: #666; line-height: 1.6; margin-bottom: 20px; }
        .read-more { background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; }
        .posts-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 30px; }
        .post-card { background: white; border-radius: 12px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); transition: transform 0.3s ease; }
        .post-card:hover { transform: translateY(-5px); }
        .post-card .post-title { font-size: 1.4rem; }
        .post-card .read-more { background: transparent; color: #667eea; padding: 0; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <main class="blog-container">
        <header class="blog-header">
            <h1 class="blog-title">Latest SEO Insights</h1>
            <p class="blog-description">Stay updated with cutting-edge SEO strategies, link building techniques, and digital marketing trends.</p>
        </header>

        <section class="featured-section">
            <h2 class="section-title">Featured Article</h2>
            <article class="featured-post">
                <h3 class="post-title"><?= htmlspecialchars($featured_post['title']) ?></h3>
                <div class="post-meta">
                    <span><?= date('F j, Y', strtotime($featured_post['date'])) ?></span> • 
                    <span>by <?= htmlspecialchars($featured_post['author']) ?></span>
                </div>
                <p class="post-excerpt"><?= htmlspecialchars($featured_post['excerpt']) ?></p>
                <a href="blog-post.php?id=<?= $featured_post['id'] ?>&slug=<?= $featured_post['slug'] ?>" class="read-more">Read Full Article</a>
            </article>
        </section>

        <section class="recent-section">
            <h2 class="section-title">Recent SEO Articles</h2>
            <p style="color: #666; margin-bottom: 40px;">Discover actionable insights and proven strategies to boost your search rankings.</p>
            
            <div class="posts-grid">
                <?php foreach ($posts as $post): ?>
                <article class="post-card">
                    <h3 class="post-title"><?= htmlspecialchars($post['title']) ?></h3>
                    <div class="post-meta">
                        <span><?= date('F j, Y', strtotime($post['date'])) ?></span> • 
                        <span>by <?= htmlspecialchars($post['author']) ?></span>
                    </div>
                    <p class="post-excerpt"><?= htmlspecialchars($post['excerpt']) ?></p>
                    <a href="blog-post.php?id=<?= $post['id'] ?>&slug=<?= $post['slug'] ?>" class="read-more">Read More →</a>
                </article>
                <?php endforeach; ?>
            </div>
        </section>

        <section style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 60px 40px; border-radius: 12px; text-align: center; margin-top: 60px;">
            <h2 style="margin-bottom: 20px;">Stay Updated with SEO Trends</h2>
            <p style="margin-bottom: 30px;">Get the latest SEO insights, link building strategies, and digital marketing tips delivered to your inbox.</p>
            <form style="max-width: 500px; margin: 0 auto; display: flex; gap: 15px;">
                <input type="email" placeholder="Enter your email address" style="flex: 1; padding: 15px; border: none; border-radius: 6px;">
                <button type="submit" style="background: white; color: #667eea; border: none; padding: 15px 30px; border-radius: 6px; font-weight: 600; cursor: pointer;">Subscribe Now</button>
            </form>
            <p style="font-size: 0.9rem; opacity: 0.8; margin-top: 15px;">Join 10,000+ SEO professionals. Unsubscribe anytime.</p>
        </section>
    </main>

    <?php include '../includes/footer.php'; ?>
</body>
</html>
